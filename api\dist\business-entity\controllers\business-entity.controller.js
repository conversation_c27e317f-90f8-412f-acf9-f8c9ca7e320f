"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessEntityController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const dtos_1 = require("../dtos");
const business_entity_level_dto_1 = require("../dtos/response/business-entity-level.dto");
const business_entity_role_dto_1 = require("../dtos/response/business-entity-role.dto");
let BusinessEntityController = class BusinessEntityController {
    constructor(businessEntityService) {
        this.businessEntityService = businessEntityService;
    }
    getBusinessEntitiesForGivenPermissionForUser(request, permission, parentId, lastLevel) {
        return this.businessEntityService.getBusinessEntitiesForGivenPermissionForUser(request.currentContext, permission, parentId, lastLevel);
    }
    getAllBusinessEntityLevels() {
        return this.businessEntityService.getAllBusinessEntityLevels();
    }
    getAllBusinessEntityRoles(entityLevel) {
        return this.businessEntityService.getAllBusinessEntityRoles(entityLevel);
    }
    getBSAOfSelectedEntity(entityId) {
        return this.businessEntityService.getBSAOfSelectedEntity(entityId);
    }
};
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities for a given permission for a user',
        type: dtos_1.BusinessEntityResponseDto,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'permission',
        type: String,
        description: 'User Permission.',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'parentId',
        type: Number,
        description: 'Business Entity Parent Id (optional).',
        required: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'lastLevel',
        type: String,
        description: 'Last Level To Get Till That Level Of Hierarchy (optional).',
        required: false,
    }),
    (0, common_1.Get)(''),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('permission')),
    __param(2, (0, common_1.Query)('parentId')),
    __param(3, (0, common_1.Query)('lastLevel')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Number, String]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getBusinessEntitiesForGivenPermissionForUser", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities for a given permission for a user',
        type: [business_entity_level_dto_1.BusinessEntityLevelResponseDto],
    }),
    (0, common_1.Get)('/entity-levels'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getAllBusinessEntityLevels", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get all business entities roles for given entity level',
        type: [business_entity_role_dto_1.BusinessEntityRoleResponseDto],
    }),
    (0, common_1.Get)('/entity-roles'),
    __param(0, (0, common_1.Query)('entityLevel')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getAllBusinessEntityRoles", null);
__decorate([
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get BSA users for given entity id',
    }),
    (0, common_1.Get)('/:entityId/BSA'),
    __param(0, (0, common_1.Query)('entityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", void 0)
], BusinessEntityController.prototype, "getBSAOfSelectedEntity", null);
BusinessEntityController = __decorate([
    (0, swagger_1.ApiTags)('Business Entity APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('business-entities'),
    __metadata("design:paramtypes", [services_1.BusinessEntityService])
], BusinessEntityController);
exports.BusinessEntityController = BusinessEntityController;
//# sourceMappingURL=business-entity.controller.js.map