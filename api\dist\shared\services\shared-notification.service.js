"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedNotificationService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../clients");
const helpers_1 = require("../helpers");
const template_placeholder_replacer_helper_1 = require("../helpers/template-placeholder-replacer.helper");
let SharedNotificationService = class SharedNotificationService {
    constructor(adminApiClient, notificationApiClient) {
        this.adminApiClient = adminApiClient;
        this.notificationApiClient = notificationApiClient;
    }
    sendNotification(entityId, entityType, recipients, templateName, placeholderValues, isApprovalEmail = false, approvalTaskId, subjectPrefix = '') {
        return __awaiter(this, void 0, void 0, function* () {
            const { to, cc, bcc } = recipients;
            const templateDetails = yield this.adminApiClient.getNotificationTemplate(templateName);
            const { body, subject } = templateDetails;
            placeholderValues = Object.assign(Object.assign({}, placeholderValues), { current_date: (0, helpers_1.currentDateInDDMMYYYY)() });
            const subjectWithReplacedPlaceholderValues = (0, template_placeholder_replacer_helper_1.templatePlaceholderReplacer)(subject, placeholderValues);
            const bodyWithReplacedPlaceholderValues = (0, template_placeholder_replacer_helper_1.templatePlaceholderReplacer)(body, placeholderValues);
            const payload = Object.assign(Object.assign(Object.assign(Object.assign({ entity_id: entityId, entity_type: entityType, subject: subjectPrefix + subjectWithReplacedPlaceholderValues, is_approval_email: isApprovalEmail, receiver: to.join(';') }, (!!cc && { cc: cc.join(';') })), (!!bcc && { bcc: bcc.join(';') })), (!!approvalTaskId && { approval_task_id: approvalTaskId })), { body: bodyWithReplacedPlaceholderValues });
            console.log('payload', payload);
            yield this.notificationApiClient.sendNotification(payload);
        });
    }
};
SharedNotificationService = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)((0, common_1.forwardRef)(() => clients_1.NotificationApiClient))),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        clients_1.NotificationApiClient])
], SharedNotificationService);
exports.SharedNotificationService = SharedNotificationService;
//# sourceMappingURL=shared-notification.service.js.map