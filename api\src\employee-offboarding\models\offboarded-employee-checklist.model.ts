import { <PERSON><PERSON>sTo, Column, DataType, ForeignKey, Table } from "sequelize-typescript";
import { BaseModel } from "src/shared/models";
import { ALLOWED_ACTION_TYPE } from "src/shared/types";
import { OffboardedEmployeeDetail } from "./offboarded-employees-detail.model";

@Table({
    tableName: 'data_offboarded_employee_checklists',
})
export class OffboardedEmployeeChecklist extends BaseModel<OffboardedEmployeeChecklist> {

    @ForeignKey(() => OffboardedEmployeeDetail)
    @Column({ field: 'offboarding_employee_detail_id', type: DataType.BIGINT, allowNull: false })
    public offboardingEmployeeDetailId: number;

    @BelongsTo(() => OffboardedEmployeeDetail)
    public offboardedEmployeeDetail: OffboardedEmployeeDetail;

    @Column({ field: 'checklist_title', type: DataType.STRING, allowNull: false })
    public checklistTitle: string;

    @Column({ field: 'group_display_name', type: DataType.STRING, allowNull: false })
    public groupDisplayName: string;

    @Column({ field: 'role_key', type: DataType.STRING, allowNull: false })
    public roleKey: string;

    @Column({ field: 'role_type', type: DataType.STRING, allowNull: false })
    public roleType: string;

    @Column({ field: 'allowed_actions', type: DataType.JSONB, allowNull: false })
    public allowedActions: ALLOWED_ACTION_TYPE[];

    @Column({ field: 'dependant_checklist_codes', type: DataType.JSONB, allowNull: true })
    public dependantChecklistCodes: string[];

    @Column({ field: 'code', type: DataType.STRING, allowNull: false })
    public code: string;

    @Column({ field: 'sla', type: DataType.STRING, allowNull: true })
    public sla: string;

    @Column({ field: 'assigned_on', allowNull: true, type: 'TIMESTAMP' })
    public assignedOn?: Date | null;

    @Column({ field: 'action_on', allowNull: true, type: 'TIMESTAMP' })
    public actionOn?: Date | null;

    @Column({ field: 'action_by', type: DataType.STRING, allowNull: true })
    public actionBy: string;

    @Column({ field: 'action_detail', type: DataType.JSONB, allowNull: true })
    public actionDetail: any;

    @Column({ field: 'visibility_permission', type: DataType.JSONB, allowNull: true })
    public visibilityPermission: string[];
}