import { Injectable } from '@nestjs/common';
import { AdminApiClient } from 'src/shared/clients';
import { instanceToPlain, multiObjectToInstance } from 'src/shared/helpers';
import { CurrentContext } from 'src/shared/types';
import { BusinessEntityResponseDto, BusinessEntityRoleUserResponseDto } from '../dtos';
import { BusinessEntityLevelResponseDto } from '../dtos/response/business-entity-level.dto';
import { BusinessEntityRoleResponseDto } from '../dtos/response/business-entity-role.dto';
import { ROLES } from 'src/shared/enums';

@Injectable()
export class BusinessEntityService {
	constructor(
		private readonly adminApiClient: AdminApiClient
	) { }

	/**
	 * Get all the locations or business entities hierarchy for given permission of a user
	 * @param currentContext
	 * @param permission
	 * @returns
	 */
	public async getBusinessEntitiesForGivenPermissionForUser(
		currentContext: CurrentContext,
		permission: string = null,
		parentId: number = null,
		lastLevel: string = null,
	) {
		const { username } = currentContext.user;
		let data;
		permission = null;

		if (permission) {
			data = await this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(
				username,
				permission,
				parentId,
				lastLevel,
			);
		} else {
			data = await this.adminApiClient.getAllBusinessHierarchy();
		}

		const response = new BusinessEntityResponseDto(data);
		return instanceToPlain(response);
	}

	public async getBusinessEntitiesChildIds(entities: number[]): Promise<number[]> {
		let allChildEntities = [...entities];
		for (const entityId of entities) {
			const childs = await this.adminApiClient.getChildernListOfBusinessEntity(entityId);
			allChildEntities.push(...childs);
		}
		return [...new Set(allChildEntities)];
	}

	/**
	 * Get all business entity levels.
	 * @returns
	 */
	public async getAllBusinessEntityLevels(): Promise<Record<string, any>> {
		let data = await this.adminApiClient.getBusinessEntityLevels();
		data = data.sort((a, b) => a.order_number - b.order_number);
		return data.map(d => instanceToPlain(new BusinessEntityLevelResponseDto(d)));
	}

	/**
	 * Get all business entity roles.
	 * @returns
	 */
	public async getAllBusinessEntityRoles(entityLevel: string): Promise<Record<string, any>> {
		const data = await this.adminApiClient.getBusinessEntityRoles(entityLevel);
		return data.map(d => instanceToPlain(new BusinessEntityRoleResponseDto(d)));
	}

	public async getBSAOfSelectedEntity(entityId: number) {
		const users = await this.adminApiClient.getUsersByRoleOfAnEntity(ROLES.BSA, entityId);

		return users.map(d => instanceToPlain(new BusinessEntityRoleUserResponseDto(d)));

		return multiObjectToInstance(BusinessEntityRoleUserResponseDto, users);
	}
}
