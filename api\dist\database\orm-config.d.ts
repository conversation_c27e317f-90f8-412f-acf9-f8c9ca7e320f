import { MasterA<PERSON><PERSON>r, MasterRoleChecklist, OffboardedEmployeeApprover, OffboardedEmployeeChecklist, OffboardedEmployeeDetail } from "src/employee-offboarding/models";
export declare const getSequelizeOrmConfig: (enableSSL?: boolean) => {
    ssl: boolean;
    dialectOptions: {
        ssl: {
            require: boolean;
        };
    };
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof OffboardedEmployeeApprover | typeof OffboardedEmployeeDetail | typeof OffboardedEmployeeChecklist | typeof MasterRoleChecklist | typeof MasterApprover)[];
} | {
    ssl?: undefined;
    dialectOptions?: undefined;
    synchronize: boolean;
    autoLoadModels: boolean;
    models: (typeof OffboardedEmployeeApprover | typeof OffboardedEmployeeDetail | typeof OffboardedEmployeeChecklist | typeof MasterRoleChecklist | typeof MasterApprover)[];
};
