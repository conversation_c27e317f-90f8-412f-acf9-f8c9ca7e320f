import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { EmployeeListResponseDto } from "./paginated-location-list-response.dto";
import { APPROVER_STATUS } from "src/shared/enums";

class ApproverResponse {
    @ApiProperty()
    @Expose()
    public id: number;

    @ApiProperty()
    @Expose()
    public title: string;

    @ApiProperty()
    @Expose()
    public isChecklistApprover: boolean;

    @ApiProperty()
    @Expose()
    public role: string;

    @ApiProperty()
    @Expose()
    public approvalSequence: number;

    @ApiProperty()
    @Expose()
    public status: APPROVER_STATUS;

    @ApiProperty()
    @Expose()
    public comment: string;

    @ApiProperty()
    @Expose()
    public assignedOn: Date;

    @ApiProperty()
    @Expose()
    public actionOn: Date;

    @ApiProperty()
    @Expose()
    public actionBy: string;
}

class ChecklistResponse {
    @ApiProperty()
    @Expose()
    public id: number;

    @ApiProperty()
    @Expose()
    public checklistTitle: string;

    @ApiProperty()
    @Expose()
    public groupDisplayName: string;

    @ApiProperty()
    @Expose()
    public roleKey: string;

    @ApiProperty()
    @Expose()
    public roleType: string;

    @ApiProperty()
    @Expose()
    public dependantChecklistCodes: string[];

    @ApiProperty()
    @Expose()
    public code: string;

    @ApiProperty()
    @Expose()
    public sla: string;

    @ApiProperty()
    @Expose()
    public assignedOn: Date;

    @ApiProperty()
    @Expose()
    public actionOn: Date;

    @ApiProperty()
    @Expose()
    public actionBy: string;

    @ApiProperty()
    @Expose()
    public actionDetail: any;

    @ApiProperty()
    @Expose()
    public visibilityPermission: string[];


}

export class EmployeeDetailResponseDto {
    @ApiProperty()
    @Expose()
    public exitDetails: EmployeeListResponseDto;

    @ApiProperty({ type: ApproverResponse, isArray: true })
    @Expose()
    @Type(() => ApproverResponse)
    public approvers: ApproverResponse[];

    @ApiProperty()
    @Expose()
    public checklists: Record<string, ChecklistResponse[]>;
}