import { BaseModel } from "src/shared/models";
import { OffboardedEmployeeDetail } from "./offboarded-employees-detail.model";
import { APPROVER_STATUS } from "src/shared/enums";
export declare class OffboardedEmployeeApprover extends BaseModel<OffboardedEmployeeApprover> {
    offboardingEmployeeDetailId: number;
    offboardedEmployeeDetail: OffboardedEmployeeDetail;
    title: string;
    isChecklistApprover: boolean;
    role: string;
    approvalSequence: number;
    status: APPROVER_STATUS;
    comment: string;
    assignedOn?: Date | null;
    actionOn?: Date | null;
    actionBy: string;
}
