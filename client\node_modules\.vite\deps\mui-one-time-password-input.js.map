{"version": 3, "sources": ["../../mui-one-time-password-input/dist/mui-one-time-password-input.es.js"], "sourcesContent": ["import { jsx as R } from \"react/jsx-runtime\";\nimport g from \"react\";\nimport { styled as W } from \"@mui/material/styles\";\nimport q from \"@mui/material/TextField\";\nimport z from \"@mui/material/Box\";\nconst G = W(q)`\n  input {\n    text-align: center;\n  }\n`, J = {\n  TextFieldStyled: G\n}, Q = (n) => /* @__PURE__ */ R(J.TextFieldStyled, { ...n }), D = {\n  left: \"ArrowLeft\",\n  right: \"ArrowRight\",\n  backspace: \"Backspace\",\n  home: \"Home\",\n  end: \"End\"\n};\nfunction U(n, s) {\n  return n <= 0 ? [] : Array.from({ length: n }, s);\n}\nfunction X(n, s, l) {\n  return n.map((i, F) => s === F ? l : i);\n}\nfunction P(n) {\n  return n.join(\"\");\n}\nfunction M(n, s) {\n  return [...n, s];\n}\nfunction Z(n, s, l) {\n  return n.reduce(\n    (i, F, C) => {\n      const { characters: y, restArrayMerged: d } = i;\n      if (C < l)\n        return {\n          restArrayMerged: d,\n          characters: M(y, F)\n        };\n      const [V, ...E] = d;\n      return {\n        restArrayMerged: E,\n        characters: M(y, V || \"\")\n      };\n    },\n    {\n      restArrayMerged: s,\n      characters: []\n    }\n  ).characters;\n}\nfunction v(n) {\n  return (s) => {\n    n.forEach((l) => {\n      typeof l == \"function\" ? l(s) : l != null && (l.current = s);\n    });\n  };\n}\nfunction ee(n) {\n  return n.split(\"\");\n}\nfunction N(n) {\n  const s = g.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  return g.useInsertionEffect(() => {\n    s.current = n;\n  }), g.useCallback((...l) => s.current?.(...l), []);\n}\nconst te = () => !0, le = g.forwardRef(\n  (n, s) => {\n    const {\n      value: l = \"\",\n      length: i = 4,\n      autoFocus: F = !1,\n      onChange: C,\n      TextFieldsProps: y,\n      onComplete: d,\n      validateChar: V = te,\n      className: E,\n      onBlur: b,\n      ...K\n    } = n, j = g.useRef(l), w = N(d), I = N((e) => {\n      const t = e.slice(0, i);\n      return {\n        isCompleted: t.length === i,\n        finalValue: t\n      };\n    });\n    g.useEffect(() => {\n      const { isCompleted: e, finalValue: t } = I(\n        j.current\n      );\n      e && w(t);\n    }, [i, w, I]);\n    const p = U(\n      i,\n      (e, t) => ({\n        character: l[t] || \"\",\n        inputRef: g.createRef()\n      })\n    ), T = (e) => p.findIndex(({ inputRef: t }) => t.current === e), k = () => p.map(({ character: e }) => e), A = (e, t) => {\n      const r = X(\n        k(),\n        e,\n        t\n      );\n      return P(r);\n    }, $ = (e) => {\n      p[e]?.inputRef.current?.focus();\n    }, c = (e) => {\n      p[e]?.inputRef.current?.select();\n    }, O = (e) => {\n      e + 1 !== i && (p[e + 1].character ? c(e + 1) : $(e + 1));\n    }, S = (e, t) => typeof V != \"function\" ? !0 : V(e, t), Y = (e) => {\n      const t = T(e.target);\n      if (t === 0 && e.target.value.length > 1) {\n        const { finalValue: m, isCompleted: B } = I(\n          e.target.value\n        );\n        C?.(m), B && d?.(m), c(m.length - 1);\n        return;\n      }\n      const r = e.target.value[0] || \"\";\n      let u = r;\n      u && !S(u, t) && (u = \"\");\n      const a = A(t, u);\n      C?.(a);\n      const { isCompleted: h, finalValue: f } = I(a);\n      h && d?.(f), u !== \"\" ? a.length - 1 < t ? c(a.length) : O(t) : r === \"\" && a.length <= t && c(t - 1);\n    }, _ = (e) => {\n      const t = e.target, r = t.selectionStart, u = t.selectionEnd, a = T(t), h = r === 0 && u === 0;\n      if (t.value === e.key)\n        e.preventDefault(), O(a);\n      else if (D.backspace === e.key) {\n        if (!t.value)\n          e.preventDefault(), c(a - 1);\n        else if (h) {\n          e.preventDefault();\n          const f = A(a, \"\");\n          C?.(f), f.length <= a && c(a - 1);\n        }\n      } else D.left === e.key ? (e.preventDefault(), c(a - 1)) : D.right === e.key ? (e.preventDefault(), c(a + 1)) : D.home === e.key ? (e.preventDefault(), c(0)) : D.end === e.key && (e.preventDefault(), c(p.length - 1));\n    }, H = (e) => {\n      const t = e.clipboardData.getData(\"text/plain\"), r = e.target, u = p.findIndex(\n        ({ character: x, inputRef: o }) => x === \"\" || o.current === r\n      ), a = k(), h = Z(\n        a,\n        ee(t),\n        u\n      ).map((x, o) => S(x, o) ? x : \"\"), f = P(h);\n      C?.(f);\n      const { isCompleted: m, finalValue: B } = I(f);\n      m ? (d?.(B), c(i - 1)) : c(f.length);\n    }, L = (e) => {\n      if (!p.some(({ inputRef: r }) => r.current === e.relatedTarget)) {\n        const { isCompleted: r, finalValue: u } = I(l);\n        b?.(u, r);\n      }\n    };\n    return /* @__PURE__ */ R(\n      z,\n      {\n        display: \"flex\",\n        gap: \"20px\",\n        alignItems: \"center\",\n        ref: s,\n        className: `MuiOtpInput-Box ${E || \"\"}`,\n        ...K,\n        children: p.map(({ character: e, inputRef: t }, r) => {\n          const {\n            onPaste: u,\n            onFocus: a,\n            onKeyDown: h,\n            className: f,\n            onBlur: m,\n            inputRef: B,\n            ...x\n          } = typeof y == \"function\" ? y(r) || {} : y || {};\n          return /* @__PURE__ */ R(\n            Q,\n            {\n              autoFocus: F ? r === 0 : !1,\n              autoComplete: \"one-time-code\",\n              value: e,\n              inputRef: v([t, B]),\n              className: `MuiOtpInput-TextField MuiOtpInput-TextField-${r + 1} ${f || \"\"}`,\n              onPaste: (o) => {\n                o.preventDefault(), H(o), u?.(o);\n              },\n              onFocus: (o) => {\n                o.preventDefault(), o.target.select(), a?.(o);\n              },\n              onChange: Y,\n              onKeyDown: (o) => {\n                _(o), h?.(o);\n              },\n              onBlur: (o) => {\n                m?.(o), L(o);\n              },\n              ...x\n            },\n            r\n          );\n        })\n      }\n    );\n  }\n);\nexport {\n  le as MuiOtpInput\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,yBAAyB;AACzB,mBAAc;AAId,IAAM,IAAI,eAAE,iBAAC;AAAA;AAAA;AAAA;AAAA;AAAb,IAIG,IAAI;AAAA,EACL,iBAAiB;AACnB;AANA,IAMG,IAAI,CAAC,UAAsB,mBAAAA,KAAE,EAAE,iBAAiB,EAAE,GAAG,EAAE,CAAC;AAN3D,IAM8D,IAAI;AAAA,EAChE,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,MAAM;AAAA,EACN,KAAK;AACP;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC;AAClD;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,EAAE,IAAI,CAAC,GAAG,MAAM,MAAM,IAAI,IAAI,CAAC;AACxC;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,EAAE,KAAK,EAAE;AAClB;AACA,SAAS,EAAE,GAAG,GAAG;AACf,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AACA,SAAS,EAAE,GAAG,GAAG,GAAG;AAClB,SAAO,EAAE;AAAA,IACP,CAAC,GAAG,GAAG,MAAM;AACX,YAAM,EAAE,YAAY,GAAG,iBAAiB,EAAE,IAAI;AAC9C,UAAI,IAAI;AACN,eAAO;AAAA,UACL,iBAAiB;AAAA,UACjB,YAAY,EAAE,GAAG,CAAC;AAAA,QACpB;AACF,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI;AAClB,aAAO;AAAA,QACL,iBAAiB;AAAA,QACjB,YAAY,EAAE,GAAG,KAAK,EAAE;AAAA,MAC1B;AAAA,IACF;AAAA,IACA;AAAA,MACE,iBAAiB;AAAA,MACjB,YAAY,CAAC;AAAA,IACf;AAAA,EACF,EAAE;AACJ;AACA,SAAS,EAAE,GAAG;AACZ,SAAO,CAAC,MAAM;AACZ,MAAE,QAAQ,CAAC,MAAM;AACf,aAAO,KAAK,aAAa,EAAE,CAAC,IAAI,KAAK,SAAS,EAAE,UAAU;AAAA,IAC5D,CAAC;AAAA,EACH;AACF;AACA,SAAS,GAAG,GAAG;AACb,SAAO,EAAE,MAAM,EAAE;AACnB;AACA,SAAS,EAAE,GAAG;AACZ,QAAM,IAAI,aAAAC,QAAE,OAAO,MAAM;AACvB,UAAM,IAAI,MAAM,+CAA+C;AAAA,EACjE,CAAC;AACD,SAAO,aAAAA,QAAE,mBAAmB,MAAM;AAChC,MAAE,UAAU;AAAA,EACd,CAAC,GAAG,aAAAA,QAAE,YAAY,IAAI,MAAG;AAnE3B;AAmE8B,mBAAE,YAAF,2BAAY,GAAG;AAAA,KAAI,CAAC,CAAC;AACnD;AACA,IAAM,KAAK,MAAM;AAAjB,IAAqB,KAAK,aAAAA,QAAE;AAAA,EAC1B,CAAC,GAAG,MAAM;AACR,UAAM;AAAA,MACJ,OAAO,IAAI;AAAA,MACX,QAAQ,IAAI;AAAA,MACZ,WAAW,IAAI;AAAA,MACf,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,cAAc,IAAI;AAAA,MAClB,WAAW;AAAA,MACX,QAAQ;AAAA,MACR,GAAG;AAAA,IACL,IAAI,GAAG,IAAI,aAAAA,QAAE,OAAO,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM;AAC7C,YAAM,IAAI,EAAE,MAAM,GAAG,CAAC;AACtB,aAAO;AAAA,QACL,aAAa,EAAE,WAAW;AAAA,QAC1B,YAAY;AAAA,MACd;AAAA,IACF,CAAC;AACD,iBAAAA,QAAE,UAAU,MAAM;AAChB,YAAM,EAAE,aAAa,GAAG,YAAY,EAAE,IAAI;AAAA,QACxC,EAAE;AAAA,MACJ;AACA,WAAK,EAAE,CAAC;AAAA,IACV,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;AACZ,UAAM,IAAI;AAAA,MACR;AAAA,MACA,CAAC,GAAG,OAAO;AAAA,QACT,WAAW,EAAE,CAAC,KAAK;AAAA,QACnB,UAAU,aAAAA,QAAE,UAAU;AAAA,MACxB;AAAA,IACF,GAAG,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,GAAG,IAAI,MAAM,EAAE,IAAI,CAAC,EAAE,WAAW,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,MAAM;AACvH,YAAM,IAAI;AAAA,QACR,EAAE;AAAA,QACF;AAAA,QACA;AAAA,MACF;AACA,aAAO,EAAE,CAAC;AAAA,IACZ,GAAG,IAAI,CAAC,MAAM;AA5GlB;AA6GM,oBAAE,CAAC,MAAH,mBAAM,SAAS,YAAf,mBAAwB;AAAA,IAC1B,GAAG,IAAI,CAAC,MAAM;AA9GlB;AA+GM,oBAAE,CAAC,MAAH,mBAAM,SAAS,YAAf,mBAAwB;AAAA,IAC1B,GAAG,IAAI,CAAC,MAAM;AACZ,UAAI,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE,YAAY,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;AAAA,IACzD,GAAG,IAAI,CAAC,GAAG,MAAM,OAAO,KAAK,aAAa,OAAK,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM;AACjE,YAAM,IAAI,EAAE,EAAE,MAAM;AACpB,UAAI,MAAM,KAAK,EAAE,OAAO,MAAM,SAAS,GAAG;AACxC,cAAM,EAAE,YAAY,GAAG,aAAa,EAAE,IAAI;AAAA,UACxC,EAAE,OAAO;AAAA,QACX;AACA,+BAAI,IAAI,MAAK,uBAAI,KAAI,EAAE,EAAE,SAAS,CAAC;AACnC;AAAA,MACF;AACA,YAAM,IAAI,EAAE,OAAO,MAAM,CAAC,KAAK;AAC/B,UAAI,IAAI;AACR,WAAK,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI;AACtB,YAAM,IAAI,EAAE,GAAG,CAAC;AAChB,6BAAI;AACJ,YAAM,EAAE,aAAa,GAAG,YAAY,EAAE,IAAI,EAAE,CAAC;AAC7C,YAAK,uBAAI,KAAI,MAAM,KAAK,EAAE,SAAS,IAAI,IAAI,EAAE,EAAE,MAAM,IAAI,EAAE,CAAC,IAAI,MAAM,MAAM,EAAE,UAAU,KAAK,EAAE,IAAI,CAAC;AAAA,IACtG,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,EAAE,QAAQ,IAAI,EAAE,gBAAgB,IAAI,EAAE,cAAc,IAAI,EAAE,CAAC,GAAG,IAAI,MAAM,KAAK,MAAM;AAC7F,UAAI,EAAE,UAAU,EAAE;AAChB,UAAE,eAAe,GAAG,EAAE,CAAC;AAAA,eAChB,EAAE,cAAc,EAAE,KAAK;AAC9B,YAAI,CAAC,EAAE;AACL,YAAE,eAAe,GAAG,EAAE,IAAI,CAAC;AAAA,iBACpB,GAAG;AACV,YAAE,eAAe;AACjB,gBAAM,IAAI,EAAE,GAAG,EAAE;AACjB,iCAAI,IAAI,EAAE,UAAU,KAAK,EAAE,IAAI,CAAC;AAAA,QAClC;AAAA,MACF,MAAO,GAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,eAAe,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,GAAG,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,GAAG,EAAE,EAAE,SAAS,CAAC;AAAA,IACxN,GAAG,IAAI,CAAC,MAAM;AACZ,YAAM,IAAI,EAAE,cAAc,QAAQ,YAAY,GAAG,IAAI,EAAE,QAAQ,IAAI,EAAE;AAAA,QACnE,CAAC,EAAE,WAAW,GAAG,UAAU,EAAE,MAAM,MAAM,MAAM,EAAE,YAAY;AAAA,MAC/D,GAAG,IAAI,EAAE,GAAG,IAAI;AAAA,QACd;AAAA,QACA,GAAG,CAAC;AAAA,QACJ;AAAA,MACF,EAAE,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,CAAC;AAC1C,6BAAI;AACJ,YAAM,EAAE,aAAa,GAAG,YAAY,EAAE,IAAI,EAAE,CAAC;AAC7C,WAAK,uBAAI,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,MAAM;AAAA,IACrC,GAAG,IAAI,CAAC,MAAM;AACZ,UAAI,CAAC,EAAE,KAAK,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,aAAa,GAAG;AAC/D,cAAM,EAAE,aAAa,GAAG,YAAY,EAAE,IAAI,EAAE,CAAC;AAC7C,+BAAI,GAAG;AAAA,MACT;AAAA,IACF;AACA,eAAuB,mBAAAD;AAAA,MACrB;AAAA,MACA;AAAA,QACE,SAAS;AAAA,QACT,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,KAAK;AAAA,QACL,WAAW,mBAAmB,KAAK,EAAE;AAAA,QACrC,GAAG;AAAA,QACH,UAAU,EAAE,IAAI,CAAC,EAAE,WAAW,GAAG,UAAU,EAAE,GAAG,MAAM;AACpD,gBAAM;AAAA,YACJ,SAAS;AAAA,YACT,SAAS;AAAA,YACT,WAAW;AAAA,YACX,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,GAAG;AAAA,UACL,IAAI,OAAO,KAAK,aAAa,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC;AAChD,qBAAuB,mBAAAA;AAAA,YACrB;AAAA,YACA;AAAA,cACE,WAAW,IAAI,MAAM,IAAI;AAAA,cACzB,cAAc;AAAA,cACd,OAAO;AAAA,cACP,UAAU,EAAE,CAAC,GAAG,CAAC,CAAC;AAAA,cAClB,WAAW,+CAA+C,IAAI,CAAC,IAAI,KAAK,EAAE;AAAA,cAC1E,SAAS,CAAC,MAAM;AACd,kBAAE,eAAe,GAAG,EAAE,CAAC,GAAG,uBAAI;AAAA,cAChC;AAAA,cACA,SAAS,CAAC,MAAM;AACd,kBAAE,eAAe,GAAG,EAAE,OAAO,OAAO,GAAG,uBAAI;AAAA,cAC7C;AAAA,cACA,UAAU;AAAA,cACV,WAAW,CAAC,MAAM;AAChB,kBAAE,CAAC,GAAG,uBAAI;AAAA,cACZ;AAAA,cACA,QAAQ,CAAC,MAAM;AACb,uCAAI,IAAI,EAAE,CAAC;AAAA,cACb;AAAA,cACA,GAAG;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF;AACF;", "names": ["R", "g"]}