"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UI_ROUTES = exports.NOTIFICATION_ENTITY_TYPE = exports.USER_EMAIL_GROUP = exports.EMAIL_TEMPLATES = void 0;
var EMAIL_TEMPLATES;
(function (EMAIL_TEMPLATES) {
    EMAIL_TEMPLATES["CHECKLIST_TASK"] = "EOP.EMAIL.CHECKLIST_TASK";
    EMAIL_TEMPLATES["DEPARTMENT_CHECKLIST_CLEAR"] = "EOP.EMAIL.DEPARTMENT_CHECKLIST_CLEAR";
    EMAIL_TEMPLATES["DEPENDANT_CHECKLIST_ASSIGNED"] = "EOP.EMAIL.DEPENDANT_CHECKLIST_ASSIGNED";
})(EMAIL_TEMPLATES = exports.EMAIL_TEMPLATES || (exports.EMAIL_TEMPLATES = {}));
var USER_EMAIL_GROUP;
(function (USER_EMAIL_GROUP) {
    USER_EMAIL_GROUP["PRODUCT_SUPPORT_EMAIL_GROUP"] = "ProductSupportEmailGroup";
})(USER_EMAIL_GROUP = exports.USER_EMAIL_GROUP || (exports.USER_EMAIL_GROUP = {}));
var NOTIFICATION_ENTITY_TYPE;
(function (NOTIFICATION_ENTITY_TYPE) {
    NOTIFICATION_ENTITY_TYPE["CHECKLIST_TASK"] = "EMAIL_CHECKLIST_TASK";
})(NOTIFICATION_ENTITY_TYPE = exports.NOTIFICATION_ENTITY_TYPE || (exports.NOTIFICATION_ENTITY_TYPE = {}));
var UI_ROUTES;
(function (UI_ROUTES) {
    UI_ROUTES["AVAILABLE_CAPABILITY_LIST"] = "/locations/:locationId/capabilities";
    UI_ROUTES["CAPABILITY_DETAIL"] = "/capabilities/:capabilityId";
})(UI_ROUTES = exports.UI_ROUTES || (exports.UI_ROUTES = {}));
//# sourceMappingURL=email-notification.enum.js.map