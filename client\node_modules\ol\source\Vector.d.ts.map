{"version": 3, "file": "Vector.d.ts", "sourceRoot": "", "sources": ["Vector.js"], "names": [], "mappings": "AAuBA;;;;;;;GAOG;AAEH;;;;;GAKG;AACH,+BAFoD,WAAW,SAAlD,OAAQ,eAAe,EAAE,WAAY;IAGhD;;;;OAIG;IACH,kBAJW,MAAM,YACN,WAAW,aACX,KAAK,CAAC,WAAW,CAAC,EAkB5B;IAbC;;;;OAIG;IACH,SAHU,WAAW,GAAC,SAAS,CAGT;IAEtB;;;;OAIG;IACH,UAHU,KAAK,CAAC,WAAW,CAAC,GAAC,SAAS,CAGd;CAE3B;;;;;;;8BAjCY,CAAS,IAA6B,EAA7B,OAAO,cAAc,EAAE,MAAM,EAAE,IAAM,EAAN,MAAM,EAAE,IAAuC,EAAvC,OAAO,uBAAuB,EAAE,OAAO,KAAG,KAAK,CAAC,OAAO,cAAc,EAAE,MAAM,CAAC;;;;gDAoCvF,CAAC,SAAxC,OAAQ,eAAe,EAAE,WAAY,qEACrC,CAAC,SAAS,aAAa,GAAG,CAAC,GAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;;;;oCAIxC,MAAM,EACiC,WAAW,SAAlD,OAAQ,eAAe,EAAE,WAAY,qEACrC,OAAO,eAAe,EAAE,WAAW,CAAC,OAAO,eAAe,EAAE,UAAU,EAAE,OAAO,oBAAoB,EAAE,OAAO,EAAE,MAAM,CAAC,GACjI,OAAW,eAAe,EAAE,WAAW,CAAC,OAAO,oBAAoB,EAAE,KAAK,EAAE,OAAO,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,GACpH,OAAW,eAAe,EAAE,WAAW,CAAC,OAAO,mBAAmB,EAAE,sBAAsB,EAAE,iBAAiB,CAAC,WAAW,CAAC,EAAE,MAAM,CAAC,GACnI,OAAW,eAAe,EAAE,mBAAmB,CAAC,OAAO,eAAe,EAAE,UAAU,GAAC,OAAO,oBAAoB,EAAE,KAAK,GACrH,OAAa,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,CAAC;oBAId,WAAW,SAAlD,OAAQ,eAAe,EAAE,WAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kBAtEhC,oBAAoB;AAsDtC;;;GAGG;AAEH;;;;;;;;GAQG;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2FG;AAEH;;;;;;;;;GASG;AACH,2BAFoD,WAAW,SAAlD,OAAQ,eAAe,EAAE,WAAY;IAGhD;;OAEG;IACH,sBAFW,OAAO,CAAC,WAAW,CAAC,EA2I9B;IA9HC;;OAEG;IACH,IAFU,uBAAuB,CAAC,OAAO,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAEtE;IAEP;;OAEG;IACH,MAFU,uBAAuB,CAAC,OAAO,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC,CAEpE;IAET;;OAEG;IACH,IAFU,uBAAuB,CAAC,IAAI,CAAC,CAEhC;IAEP;;;OAGG;IACH,gBAAmB;IAEnB;;;OAGG;IACH,gBAAqC;IAErC;;;OAGG;IACH,kBAAyE;IAEzE;;;OAGG;IACH,aAAuB;IAUvB;;;OAGG;IACH,kBACiE;IAKjE;;;OAGG;IACH,uBAA0D;IAE1D;;;OAGG;IACH,4BAAsC;IAEtC;;;OAGG;IACH,6BAA6B;IAE7B;;;OAGG;IACH,8BAA+B;IAE/B;;;;OAIG;IACH,iBAAkB;IAElB;;;;OAIG;IACH,kBAAmB;IAEnB;;;OAGG;IACH,2BAA4B;IAE5B;;;OAGG;IACH,4BAA+B;IAuBjC;;;;;;;;;;;OAWG;IACH,oBAHW,WAAW,QAMrB;IAED;;;;OAIG;IACH,sCAHW,WAAW,QA4BrB;IAED;;;;OAIG;IACH,2BAaC;IAED;;;;;;OAMG;IACH,oBA2BC;IAED;;;;OAIG;IACH,sBAHW,KAAK,CAAC,WAAW,CAAC,QAM5B;IAED;;;;OAIG;IACH,wCAHW,KAAK,CAAC,WAAW,CAAC,QA2C5B;IAED;;;OAGG;IACH,gCAuDC;IAED;;;;OAIG;IACH,aAHW,OAAO,QAoCjB;IAED;;;;;;;;;;;OAWG;IACH,eAHa,CAAC,YAHH,CAAS,IAAW,EAAX,WAAW,KAAG,CAAC,GAEvB,CAAC,GAAC,SAAS,CAWtB;IAED;;;;;;;;;;;;;;OAcG;IACH,iCAFa,CAAC,cAJH,OAAO,kBAAkB,EAAE,UAAU,YACrC,CAAS,IAAW,EAAX,WAAW,KAAG,CAAC,GAEvB,CAAC,GAAC,SAAS,CAetB;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,uBAHa,CAAC,UAJH,OAAO,cAAc,EAAE,MAAM,YAC7B,CAAS,IAAW,EAAX,WAAW,KAAG,CAAC,GAEvB,CAAC,GAAC,SAAS,CAWtB;IAED;;;;;;;;;;;;;;OAcG;IACH,iCAHa,CAAC,UAJH,OAAO,cAAc,EAAE,MAAM,YAC7B,CAAS,IAAW,EAAX,WAAW,KAAG,CAAC,GAEvB,CAAC,GAAC,SAAS,CAwBtB;IAED;;;;;;OAMG;IACH,yBAHY,UAAU,CAAC,WAAW,CAAC,GAAC,IAAI,CAKvC;IAED;;;;;OAKG;IACH,eAHY,KAAK,CAAC,WAAW,CAAC,CAc7B;IAED;;;;;OAKG;IACH,oCAJW,OAAO,kBAAkB,EAAE,UAAU,GACpC,KAAK,CAAC,WAAW,CAAC,CAU7B;IAED;;;;;;;;;;;;;OAaG;IACH,4BANW,OAAO,cAAc,EAAE,MAAM,eAC7B,OAAO,uBAAuB,EAAE,OAAO,GAEtC,KAAK,CAAC,WAAW,CAAC,CAqB7B;IAED;;;;;;;;;;;;OAYG;IACH,0CAPW,OAAO,kBAAkB,EAAE,UAAU,WACrC,CAAS,IAAW,EAAX,WAAW,KAAE,OAAO,GAG5B,WAAW,CA+CtB;IAED;;;;;;;;;OASG;IACH,mBALW,OAAO,cAAc,EAAE,MAAM,GAE5B,OAAO,cAAc,EAAE,MAAM,CAKxC;IAED;;;;;;;;;;;OAWG;IACH,mBAJW,MAAM,GAAC,MAAM,GACZ,mCAAmC,CAAC,WAAW,CAAC,GAAC,IAAI,CAUhE;IAED;;;;;OAKG;IACH,qBAHW,MAAM,GACL,WAAW,GAAC,IAAI,CAK3B;IAED;;;;;OAKG;IACH,aAHY,OAAO,sBAAsB,EAAE,OAAO,CAAC,WAAW,CAAC,GAAC,IAAI,CAKnE;IAED;;OAEG;IACH,eAFY,OAAO,CAIlB;IAED;;;;;OAKG;IACH,UAHY,MAAM,GAAC,OAAO,qBAAqB,EAAE,kBAAkB,GAAC,SAAS,CAK5E;IAED;;;OAGG;IACH,6BAuCC;IAED;;;;;OAKG;IACH,oBAJW,WAAW,GACV,OAAO,CASlB;IAED;;OAEG;IACH,WAFY,OAAO,CAYlB;IAED;;;;OAIG;IACH,qBAJW,OAAO,cAAc,EAAE,MAAM,cAC7B,MAAM,cACN,OAAO,uBAAuB,EAAE,OAAO,QAoDjD;IAWD;;;;OAIG;IACH,2BAHW,OAAO,cAAc,EAAE,MAAM,QAavC;IAED;;;;;;OAMG;IACH,yBAHW,KAAK,CAAC,WAAW,CAAC,QAW5B;IAED;;;;;;OAMG;IACH,uBAHW,WAAW,QAWrB;IAED;;;;;OAKG;IACH,yCAJW,WAAW,GACV,OAAO,CAyClB;IAED;;;;;OAKG;IACH,2BAOC;IAED;;;;;OAKG;IACH,kBAHW,OAAO,qBAAqB,EAAE,aAAa,QAKrD;IAED;;;;OAIG;IACH,YAHW,MAAM,GAAC,OAAO,qBAAqB,EAAE,kBAAkB,QAOjE;IAED;;OAEG;IACH,sBAFW,OAAO,QAKjB;CACF;0BAxpCyB,sBAAsB;uBAbzB,kBAAkB;mBAgBtB,aAAa"}