{"version": 3, "file": "ICacheManager.d.ts", "sourceRoot": "", "sources": ["../../../src/cache/interface/ICacheManager.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AACvD,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAC;AAC3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,qBAAqB,EAAE,MAAM,sCAAsC,CAAC;AAC7E,OAAO,EAAE,gBAAgB,EAAE,MAAM,iCAAiC,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAC;AAC7D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAC;AACrE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAC;AACvE,OAAO,EAAE,uBAAuB,EAAE,MAAM,wCAAwC,CAAC;AACjF,OAAO,EAAE,YAAY,EAAE,MAAM,+BAA+B,CAAC;AAE7D,MAAM,WAAW,aAAa;IAC1B;;;OAGG;IACH,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;IAErD;;;OAGG;IACH,UAAU,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC;IAEzC;;;;;;OAMG;IACH,YAAY,CACR,GAAG,EAAE,MAAM,EACX,aAAa,CAAC,EAAE,MAAM,EACtB,QAAQ,CAAC,EAAE,MAAM,GAClB,OAAO,CAAC;IAEX;;;OAGG;IACH,oBAAoB,CAAC,UAAU,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI,CAAC;IAE/D;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAC;IAEnD;;;OAGG;IACH,wBAAwB,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IAE3E;;;OAGG;IACH,wBAAwB,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAE/D;;;OAGG;IACH,yBAAyB,CACrB,eAAe,EAAE,MAAM,GACxB,kBAAkB,GAAG,IAAI,CAAC;IAE7B;;;OAGG;IACH,yBAAyB,CAAC,YAAY,EAAE,kBAAkB,GAAG,IAAI,CAAC;IAElE;;;OAGG;IACH,cAAc,CAAC,cAAc,EAAE,MAAM,GAAG,iBAAiB,GAAG,IAAI,CAAC;IAEjE;;;OAGG;IACH,cAAc,CAAC,WAAW,EAAE,iBAAiB,GAAG,IAAI,CAAC;IAErD;;;OAGG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,GAC3B,qBAAqB,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,qBAAqB,GACvC,IAAI,CAAC;IAER;;;OAGG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAElE;;OAEG;IACH,wBAAwB,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;IAE1C;;;;OAIG;IACH,oBAAoB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,uBAAuB,GAAG,IAAI,CAAC;IAExE;;;OAGG;IACH,2BAA2B,CAAC,IAAI,EAAE,MAAM,GAAG,uBAAuB,GAAG,IAAI,CAAC;IAE1E;;;OAGG;IACH,iCAAiC,CAAC,SAAS,EAAE,MAAM,GAAG,MAAM,CAAC;IAE7D;;;OAGG;IACH,kBAAkB,CAAC,kBAAkB,EAAE,MAAM,GAAG,gBAAgB,GAAG,IAAI,CAAC;IAExE;;;;OAIG;IACH,kBAAkB,CACd,kBAAkB,EAAE,MAAM,EAC1B,eAAe,EAAE,gBAAgB,GAClC,IAAI,CAAC;IAER;;OAEG;IACH,cAAc,IAAI,WAAW,EAAE,CAAC;IAEhC;;;OAGG;IACH,eAAe,CACX,WAAW,EAAE,WAAW,EACxB,YAAY,CAAC,EAAE,YAAY,GAC5B,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjB;;;;;OAKG;IACH,qBAAqB,CAAC,MAAM,EAAE,aAAa,GAAG,aAAa,EAAE,CAAC;IAE9D;;;OAGG;IACH,wBAAwB,CAAC,MAAM,EAAE,aAAa,GAAG,WAAW,GAAG,IAAI,CAAC;IAEpE;;OAEG;IACH,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAEnC;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAEjD;;;OAGG;IACH,oBAAoB,CAAC,OAAO,EAAE,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE5D;;OAEG;IACH,aAAa,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IAEjC;;OAEG;IACH,iBAAiB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAE9C;;OAEG;IACH,kBAAkB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;CACzC"}