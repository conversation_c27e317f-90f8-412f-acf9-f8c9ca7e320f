import {
  defaultTheme_default
} from "./chunk-4RXV4KIM.js";
import {
  identifier_default
} from "./chunk-UUHLHOPM.js";
import {
  useTheme_default
} from "./chunk-JCOSHRX2.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/styles/useTheme.js
var React = __toESM(require_react());
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-VIAU25KR.js.map
