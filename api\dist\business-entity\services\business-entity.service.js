"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BusinessEntityService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const helpers_1 = require("../../shared/helpers");
const dtos_1 = require("../dtos");
const business_entity_level_dto_1 = require("../dtos/response/business-entity-level.dto");
const business_entity_role_dto_1 = require("../dtos/response/business-entity-role.dto");
const enums_1 = require("../../shared/enums");
let BusinessEntityService = class BusinessEntityService {
    constructor(adminApiClient) {
        this.adminApiClient = adminApiClient;
    }
    getBusinessEntitiesForGivenPermissionForUser(currentContext, permission = null, parentId = null, lastLevel = null) {
        return __awaiter(this, void 0, void 0, function* () {
            const { username } = currentContext.user;
            let data;
            permission = null;
            if (permission) {
                data = yield this.adminApiClient.getAllBusinessHierarchyByUserAndPermission(username, permission, parentId, lastLevel);
            }
            else {
                data = yield this.adminApiClient.getAllBusinessHierarchy();
            }
            const response = new dtos_1.BusinessEntityResponseDto(data);
            return (0, helpers_1.instanceToPlain)(response);
        });
    }
    getBusinessEntitiesChildIds(entities) {
        return __awaiter(this, void 0, void 0, function* () {
            let allChildEntities = [...entities];
            for (const entityId of entities) {
                const childs = yield this.adminApiClient.getChildernListOfBusinessEntity(entityId);
                allChildEntities.push(...childs);
            }
            return [...new Set(allChildEntities)];
        });
    }
    getAllBusinessEntityLevels() {
        return __awaiter(this, void 0, void 0, function* () {
            let data = yield this.adminApiClient.getBusinessEntityLevels();
            data = data.sort((a, b) => a.order_number - b.order_number);
            return data.map(d => (0, helpers_1.instanceToPlain)(new business_entity_level_dto_1.BusinessEntityLevelResponseDto(d)));
        });
    }
    getAllBusinessEntityRoles(entityLevel) {
        return __awaiter(this, void 0, void 0, function* () {
            const data = yield this.adminApiClient.getBusinessEntityRoles(entityLevel);
            return data.map(d => (0, helpers_1.instanceToPlain)(new business_entity_role_dto_1.BusinessEntityRoleResponseDto(d)));
        });
    }
    getBSAOfSelectedEntity(entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(enums_1.ROLES.BSA, entityId);
            return (0, helpers_1.multiObjectToInstance)(dtos_1.BusinessEntityRoleUserResponseDto, users, { excludeExtraneousValues: false });
        });
    }
};
BusinessEntityService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient])
], BusinessEntityService);
exports.BusinessEntityService = BusinessEntityService;
//# sourceMappingURL=business-entity.service.js.map