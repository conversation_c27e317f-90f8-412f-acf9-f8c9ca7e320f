import SvgColor from '@/components/svg-color';
import { useLoading } from '@/hooks/use-loading';
import { useTranslate } from '@/locales/use-locales';
import TaskDetailsDialog from '@/modules/tasks/components/actions/task-details-dialog';
import { TaskStatus } from '@/modules/tasks/enums/task-list-filters.enum';
import { ChecklistItem } from '@/shared/models/employee-exit-response.model';
import { sendReminder } from '@/shared/services/employee-exit.service';
import { fDateTime } from '@/shared/utils';
import { capitalizeSentence } from '@/shared/utils/change-case';
import { ExtentionEnum, getIcon } from '@/shared/utils/get-icon';
import { getTaskStatus, Task } from '@/shared/utils/task-utils';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Tooltip,
  Typography,
} from '@mui/material';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { useMutation } from 'react-query';

// -------------------
// Types
// -------------------
interface ChecklistTask extends ChecklistItem {
  taskOptions: Task[];
  ref?: (el: HTMLDivElement | null) => void;
}

type Props = {
  title: string;
  exitId: number;
  checklistData: ChecklistTask[];
  expanded?: boolean;
  onChange?: () => void;
  taskRefs: React.RefObject<Record<string, HTMLDivElement | null>>;
};

// -------------------
// Status-specific components
// -------------------
export function CompletedContent({ task, onViewDetails }: { task: ChecklistItem; onViewDetails: (t: any) => void }) {
  const { t } = useTranslate();
  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box component="img" src={getIcon('check-mark-green', ExtentionEnum.PNG)} sx={{ width: 18, height: 18 }} />
          <Typography variant="subtitle1" fontWeight="bold">
            {task.checklistTitle}
          </Typography>
        </Box>
        <Box sx={{ position: 'absolute', right: 16, top: '50%', transform: 'translateY(-50%)', cursor: 'pointer' }}>
          <Button variant="outlined" size="small" onClick={() => onViewDetails(task)}>
            {t('label.view_details')}
          </Button>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', gap: 6, mt: 1, pl: 3 }}>
        {/* Assigned On */}
        {task.assignedOn && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SvgColor sx={{ height: 20, width: 23 }} src={getIcon('calendar', ExtentionEnum.PNG)} />
              <Typography variant="label" fontWeight="bold">
                {t('label.assign_on')}
              </Typography>
            </Box>
            <Typography variant="value">{fDateTime(task.assignedOn) || '-'}</Typography>
          </Box>
        )}

        {/* Action On */}
        {task.actionOn && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SvgColor sx={{ height: 20, width: 23 }} src={getIcon('calendar', ExtentionEnum.PNG)} />
              <Typography variant="label" fontWeight="bold">
                {t('label.action_on')}
              </Typography>
            </Box>
            <Typography variant="value">{fDateTime(task.actionOn) || '-'}</Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}

export function InprogressContent({
  task,
  onSubmitAction,
}: {
  task: ChecklistItem;
  onSubmitAction?: (t: any) => void;
}) {
  const { t } = useTranslate();
  return (
    <Box>
      {/* Top row: Icon + Title */}
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
        <Box
          component="img"
          src={getIcon('inprogress-new', ExtentionEnum.PNG)}
          sx={{ mt: 0.5, width: 20, height: 20 }}
        />
        <Typography variant="subtitle1" fontWeight="bold">
          {task.checklistTitle}
        </Typography>
      </Box>
      {onSubmitAction && (
        <Box sx={{ position: 'absolute', right: 16, top: '50%', transform: 'translateY(-50%)', cursor: 'pointer' }}>
          <Button variant="contained" size="small" onClick={() => onSubmitAction?.(task)}>
            {t('label.submit_action')}
          </Button>
        </Box>
      )}

      {/* Dates row - under icon + title */}
      {task.assignedOn && (
        <Box sx={{ display: 'flex', gap: 6, mt: 1, pl: 3 }}>
          {/* Assigned On */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SvgColor sx={{ height: 20, width: 23 }} src={getIcon('calendar', ExtentionEnum.PNG)} />
              <Typography variant="label" fontWeight="bold">
                {t('label.assign_on')}
              </Typography>
            </Box>
            <Typography variant="value">{fDateTime(task.assignedOn) || '-'}</Typography>
          </Box>
        </Box>
      )}
    </Box>
  );
}

export function WaitingContent({ task, messages }: { task: ChecklistItem; messages: string[] }) {
  return (
    <Box>
      {/* Top row: Icon + Title */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Box component="img" src={getIcon('warning-new', ExtentionEnum.PNG)} sx={{ width: 18, height: 18 }} />
        <Typography variant="subtitle1" fontWeight="bold">
          {task.checklistTitle}
        </Typography>
      </Box>
      {messages.length > 0 && (
        <Box sx={{ mt: 1, display: 'flex', flexDirection: 'column', gap: 1, pl: 3 }}>
          {messages.map((msg, idx) => (
            <Chip
              key={idx}
              label={msg}
              variant="outlined"
              sx={{
                background: '#FFF9E6',
                border: '1px solid #F5E8BD',
                borderRadius: '6px',
                fontSize: '0.8rem',
                fontWeight: 'bold',
                color: '#3A3A42',
                justifyContent: 'flex-start',
              }}
            />
          ))}
        </Box>
      )}
    </Box>
  );
}

// -------------------
// Main Accordion Component
// -------------------
const OffboardingChecklistAccordion = ({ exitId, title, checklistData, expanded, onChange, taskRefs }: Props) => {
  const { t } = useTranslate();
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any | null>(null);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [selectedDept, setSelectedDept] = useState<string | null>(null);
  const { setLoading, setMessage } = useLoading();
  // const taskRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const handleReminderClick = (e: React.MouseEvent, deptTitle: string) => {
    e.stopPropagation();
    setSelectedDept(deptTitle);
    setOpenConfirm(true);
  };

  const handleOpenDetails = (task: ChecklistItem) => {
    // Prepare mandatory fields
    const mandatoryFields = [
      { label: 'Assigned On', type: 'ASSIGNED_ON', value: task.assignedOn },
      { label: 'Action On', type: 'ACTION_ON', value: task.actionOn },
      { label: 'Action By', type: 'ACTION_BY', value: task.actionBy },
    ].filter((field) => field.value);

    // Extract actionDetail fields
    const actionDetailFields =
      task.actionDetail?.map((action: any) => ({
        ...action,
      })) || [];

    // Combine for dialog
    const combinedFields = [...mandatoryFields, ...actionDetailFields];

    setSelectedTask({ ...task, dialogFields: combinedFields });
    setOpenDetails(true);
  };

  const handleCloseDetails = () => {
    setOpenDetails(false);
    setSelectedTask(null);
  };

  const { mutateAsync } = useMutation({
    mutationFn: sendReminder,
    onSuccess: (response: any) => {
      enqueueSnackbar(response.message, { variant: 'success' });
      setLoading?.(false);
      setMessage?.('');
    },
    onError: (error: { message: string }) => {
      enqueueSnackbar(
        error?.message && typeof error?.message === 'string' ? error.message : t('error_messages.something_went_wrong'),
        { variant: 'error' },
      );
      setLoading?.(false);
      setMessage?.('');
    },
  });

  const handleSendReminder = async () => {
    if (!exitId || !checklistData?.length) return;
    try {
      setLoading?.(true);
      setMessage?.(t('label.submitting'));

      const roleKey = checklistData[0]?.roleKey;

      const payload = {
        exitId,
        roleKey,
      };

      await mutateAsync(payload);
    } catch (err: any) {
      enqueueSnackbar(err?.message || t('error_messages.something_went_wrong'), { variant: 'error' });
    } finally {
      setLoading?.(false);
      setMessage?.('');
    }
  };

  const getDepartmentStatus = (tasks: ChecklistTask[]) => {
    const total = tasks.length;
    const completed = tasks.filter((t) => getTaskStatus(t, checklistData).status === TaskStatus.COMPLETED).length;

    if (completed === total && total > 0) {
      return { label: t('label.all_completed'), color: '#E2F6D2' };
    }

    return { label: `${completed} out of ${total} ${t('label.completed')}`, color: '#FFF4CE' };
  };

  const { label, color } = getDepartmentStatus(checklistData);

  return (
    <>
      <Accordion
        sx={{ background: '#FFFFFF', border: '1px solid #D5D5DD', borderRadius: '11px' }}
        expanded={expanded}
        onChange={onChange}
      >
        <AccordionSummary
          sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}
          expandIcon={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box component="img" src={getIcon('down-arrow', ExtentionEnum.PNG)} sx={{ height: 24 }} />
            </Box>
          }
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {label === t('label.all_completed') ? (
              <SvgColor sx={{ height: 18, width: 18 }} src={getIcon('Tick', ExtentionEnum.PNG)} />
            ) : (
              <Tooltip title={t('label.send_remainder')}>
                <SvgColor
                  onClick={(e) => handleReminderClick(e, title)}
                  sx={{ height: 20, width: 20, cursor: 'pointer' }}
                  src={getIcon('reminder', ExtentionEnum.PNG)}
                />
              </Tooltip>
            )}

            <Typography variant="h6" fontWeight="bold">
              {title}
            </Typography>

            {/* Status Chip */}
            <Typography
              sx={{
                px: 1.5,
                py: 0.5,
                background: color,
                border: '1px solid #D5D5DD',
                borderRadius: '6px',
                fontSize: '0.875rem',
              }}
            >
              {label}
            </Typography>
          </Box>
        </AccordionSummary>

        <AccordionDetails sx={{ borderRadius: '17px' }}>
          {checklistData.map((task, index) => {
            const { status, messages } = getTaskStatus(task, task.taskOptions);
            const refKey = `${title}-${task.id}`;
            return (
              <Box
                key={task.id}
                ref={(el: HTMLDivElement | null) => {
                  taskRefs.current[refKey] = el;
                }}
              >
                {/* Dynamic Task Content */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 1.5,
                    pl: 3,
                    mb: 1,
                    position: 'relative',
                  }}
                >
                  {status === TaskStatus.COMPLETED && (
                    <CompletedContent task={task} onViewDetails={handleOpenDetails} />
                  )}
                  {status === TaskStatus.IN_PROGRESS && <InprogressContent task={task} />}
                  {status === TaskStatus.WAITING && <WaitingContent task={task} messages={messages} />}
                </Box>

                {index < checklistData.length - 1 && <Divider sx={{ my: 2 }} />}
              </Box>
            );
          })}
        </AccordionDetails>
      </Accordion>

      {/* Reminder Dialog */}
      <Dialog open={openConfirm} onClose={() => setOpenConfirm(false)}>
        <DialogTitle>{t('label.reminder_confirmation_title')}</DialogTitle>
        <DialogContent>
          {t('label.reminder_confirmation_desc')} <b>{capitalizeSentence(selectedDept as string)}</b>?
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenConfirm(false)}>{t('btn_name.cancel')}</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              setOpenConfirm(false);
              handleSendReminder();
            }}
          >
            {t('btn_name.send')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Task Details Dialog */}
      <TaskDetailsDialog
        open={openDetails}
        onClose={handleCloseDetails}
        fields={selectedTask?.dialogFields || []}
        title={selectedTask?.checklistTitle || 'Task Details'}
      />
    </>
  );
};

export default OffboardingChecklistAccordion;
