"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeOffboardingService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const dtos_1 = require("../dtos");
const exceptions_1 = require("../../shared/exceptions");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const dtos_2 = require("../../employee-detail/dtos");
const dtos_3 = require("../../business-entity/dtos");
const pagination_1 = require("../../core/pagination");
const repositories_1 = require("../repositories");
const emloyee_complete_detail_response_dto_1 = require("../dtos/response/emloyee-complete-detail-response.dto");
let EmployeeOffboardingService = class EmployeeOffboardingService {
    constructor(adminApiClient, employeeDataApiClient, offboardedEmployeeDetailRepository, databaseHelper, masterApproverRepository, masterRoleChecklistRepository, offboardingEmployeeApproverRepository, offboardingEmployeeChecklistRepository, historyApiClient) {
        this.adminApiClient = adminApiClient;
        this.employeeDataApiClient = employeeDataApiClient;
        this.offboardedEmployeeDetailRepository = offboardedEmployeeDetailRepository;
        this.databaseHelper = databaseHelper;
        this.masterApproverRepository = masterApproverRepository;
        this.masterRoleChecklistRepository = masterRoleChecklistRepository;
        this.offboardingEmployeeApproverRepository = offboardingEmployeeApproverRepository;
        this.offboardingEmployeeChecklistRepository = offboardingEmployeeChecklistRepository;
        this.historyApiClient = historyApiClient;
    }
    getEmployeeOffboardingList(page = 1, limit = 10, orderBy = 'updatedOn', orderDirection = 'DESC', filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.offboardedEmployeeDetailRepository.getEmployeeOffboardingListByFilter(filterDto, page, limit, orderBy, orderDirection);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.EmployeeListResponseDto, rows, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    initiateNewExit(newExitRequest, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, employeeEmailId, lastPhysicalWorkingDate, lastEmploymentDate, leavingGroup, transferringGroup, noticePeriod, noticeMonths, exitType, note } = newExitRequest;
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const employeeDetail = yield this.employeeDataApiClient.getEmployee(employeeEmailId);
                if (!employeeDetail) {
                    throw new exceptions_1.HttpException('Employee doesn\'t exist in oracle system.', enums_1.HttpStatus.NOT_FOUND);
                }
                const offboardedEmployeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetail(employeeDetail.work_email);
                if (offboardedEmployeeDetail) {
                    throw new exceptions_1.HttpException('The employee exit process has already been initiated.', enums_1.HttpStatus.CONFLICT);
                }
                const entityDetail = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
                if (!entityDetail) {
                    throw new exceptions_1.HttpException('Entity does not exist.', enums_1.HttpStatus.NOT_FOUND);
                }
                const bsaUserList = yield this.adminApiClient.getUsersByRoleOfAnEntity(enums_1.ROLES.BSA, entityId);
                if (!bsaUserList || bsaUserList.length === 0) {
                    throw new exceptions_1.HttpException('No BSA found for the selected entity.', enums_1.HttpStatus.NOT_FOUND);
                }
                const payload = {
                    employeeDetail: (0, helpers_1.singleObjectToInstance)(dtos_2.BasicEmployeeDetailResponseDto, employeeDetail),
                    entityId: entityDetail.id,
                    entityCode: entityDetail.code,
                    entityTitle: entityDetail.full_name,
                    bsaDetail: (0, helpers_1.multiObjectToInstance)(dtos_3.BusinessEntityRoleUserResponseDto, bsaUserList),
                    lastPhysicalWorkingDate,
                    lastEmploymentDate,
                    leavingGroup,
                    transferringGroup: transferringGroup,
                    isNoticePeriodServed: noticePeriod,
                    noticePeriod: noticeMonths,
                    exitType,
                    note,
                    status: enums_1.OFFBOARDING_STATUS.INITIATED,
                    userStatus: 'Pending Checklist Clearance'
                };
                const newEntry = yield this.offboardedEmployeeDetailRepository.initiateNewExit(payload, currentContext);
                const parentEntityDetail = yield this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, enums_1.HIERARCHY_ENTITY_TYPE.BUSINESS_UNIT);
                if (!parentEntityDetail) {
                    throw new exceptions_1.HttpException('Invalid depatment selected.', enums_1.HttpStatus.NOT_FOUND);
                }
                yield this.generateApprovers(newEntry.id, parentEntityDetail.id, currentContext);
                yield this.generateChecklist(newEntry.id, parentEntityDetail.id, currentContext);
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newEntry.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'Exit initiated',
                    additional_info: newEntry
                });
                return {
                    message: 'New exit initiated successfully.',
                    data: newEntry
                };
            }));
        });
    }
    getEmployeeExitDetail(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const employeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetailById(id);
            if (employeeDetail) {
                const { approvers, checklists } = employeeDetail, exitDetails = __rest(employeeDetail, ["approvers", "checklists"]);
                const groupedChecklist = checklists.reduce((acc, curr) => {
                    acc[curr.groupDisplayName] = acc[curr.groupDisplayName] || [];
                    acc[curr.groupDisplayName].push(curr);
                    return acc;
                }, {});
                const sortedApprovers = approvers.sort((a, b) => a.approvalSequence - b.approvalSequence);
                const response = {
                    exitDetails,
                    approvers: sortedApprovers,
                    checklists: groupedChecklist
                };
                return (0, helpers_1.singleObjectToInstance)(emloyee_complete_detail_response_dto_1.EmployeeDetailResponseDto, response);
            }
            throw new exceptions_1.HttpException('Employee detail does not exist.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    generateApprovers(employeeId, entityId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const masterApprovers = yield this.masterApproverRepository.getEntityMasterApprovers(entityId);
            const approverPayload = masterApprovers.map((approver) => ({
                offboardingEmployeeDetailId: employeeId,
                status: approver.approvalSequence === 1 ? enums_1.APPROVER_STATUS.IN_PROGRESS : enums_1.APPROVER_STATUS.NOT_INITIATED,
                assignedOn: approver.approvalSequence === 1 ? new Date() : null,
                approvalSequence: approver.approvalSequence,
                title: approver.title,
                isChecklistApprover: approver.isChecklistApprover,
                role: approver.role
            }));
            yield this.offboardingEmployeeApproverRepository.createExitApprovers(approverPayload, currentContext);
        });
    }
    generateChecklist(employeeId, entityId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const masterChecklists = yield this.masterRoleChecklistRepository.getEntityMasterChecklist(entityId);
            const checklistPayload = masterChecklists.map((checklist) => {
                var _a;
                return ({
                    offboardingEmployeeDetailId: employeeId,
                    checklistTitle: checklist.checklistTitle,
                    groupDisplayName: checklist.groupDisplayName,
                    roleKey: checklist.roleKey,
                    roleType: checklist.roleType,
                    allowedActions: checklist.allowedActions,
                    dependantChecklistCodes: checklist.dependantChecklistCodes,
                    code: checklist.code,
                    sla: checklist.sla,
                    visibilityPermission: checklist.visibilityPermission,
                    assignedOn: ((_a = checklist.dependantChecklistCodes) === null || _a === void 0 ? void 0 : _a.length) ? null : new Date()
                });
            });
            yield this.offboardingEmployeeChecklistRepository.createExitChecklists(checklistPayload, currentContext);
        });
    }
};
EmployeeOffboardingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        clients_1.EmployeeDataApiClient,
        repositories_1.OffboardedEmployeeDetailRepository,
        helpers_1.DatabaseHelper,
        repositories_1.MasterApproverRepository,
        repositories_1.MasterRoleChecklistRepository,
        repositories_1.OffboardingEmployeeApproverRepository,
        repositories_1.OffboardingEmployeeChecklistRepository,
        clients_1.HistoryApiClient])
], EmployeeOffboardingService);
exports.EmployeeOffboardingService = EmployeeOffboardingService;
//# sourceMappingURL=employee-offboarding.service.js.map