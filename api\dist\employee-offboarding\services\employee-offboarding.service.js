"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeOffboardingService = void 0;
const common_1 = require("@nestjs/common");
const clients_1 = require("../../shared/clients");
const dtos_1 = require("../dtos");
const types_1 = require("../../shared/types");
const exceptions_1 = require("../../shared/exceptions");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const dtos_2 = require("../../employee-detail/dtos");
const dtos_3 = require("../../business-entity/dtos");
const pagination_1 = require("../../core/pagination");
const repositories_1 = require("../repositories");
const emloyee_complete_detail_response_dto_1 = require("../dtos/response/emloyee-complete-detail-response.dto");
const config_service_1 = require("../../config/config.service");
const task_api_client_1 = require("../../shared/clients/task-api.client");
const lodash_1 = require("lodash");
const services_1 = require("../../task/services");
const services_2 = require("../../shared/services");
let EmployeeOffboardingService = class EmployeeOffboardingService {
    constructor(adminApiClient, employeeDataApiClient, offboardedEmployeeDetailRepository, databaseHelper, masterApproverRepository, masterRoleChecklistRepository, offboardingEmployeeApproverRepository, offboardingEmployeeChecklistRepository, historyApiClient, mSGraphApiClient, configService, taskApiClient, taskService, sharedNotificationService) {
        this.adminApiClient = adminApiClient;
        this.employeeDataApiClient = employeeDataApiClient;
        this.offboardedEmployeeDetailRepository = offboardedEmployeeDetailRepository;
        this.databaseHelper = databaseHelper;
        this.masterApproverRepository = masterApproverRepository;
        this.masterRoleChecklistRepository = masterRoleChecklistRepository;
        this.offboardingEmployeeApproverRepository = offboardingEmployeeApproverRepository;
        this.offboardingEmployeeChecklistRepository = offboardingEmployeeChecklistRepository;
        this.historyApiClient = historyApiClient;
        this.mSGraphApiClient = mSGraphApiClient;
        this.configService = configService;
        this.taskApiClient = taskApiClient;
        this.taskService = taskService;
        this.sharedNotificationService = sharedNotificationService;
    }
    getEmployeeOffboardingList(page = 1, limit = 10, orderBy = 'updatedOn', orderDirection = 'DESC', filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            const { rows, count } = yield this.offboardedEmployeeDetailRepository.getEmployeeOffboardingListByFilter(filterDto, page, limit, orderBy, orderDirection);
            const records = (0, helpers_1.multiObjectToInstance)(dtos_1.EmployeeListResponseDto, rows, {
                excludeExtraneousValues: true,
                enableImplicitConversion: true,
            });
            return new pagination_1.Pagination({ records, total: count });
        });
    }
    initiateNewExit(newExitRequest, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { entityId, employeeEmailId, lastPhysicalWorkingDate, lastEmploymentDate, noticePeriod, noticeMonths, exitType, note } = newExitRequest;
            return yield this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const userDetail = yield this.mSGraphApiClient.getUserDetails(employeeEmailId);
                if (!userDetail) {
                    throw new exceptions_1.HttpException('Invalid User.', enums_1.HttpStatus.NOT_FOUND);
                }
                const employeeDetail = yield this.employeeDataApiClient.getEmployee([userDetail.mail, userDetail.userPrincipalName]);
                if (!(employeeDetail === null || employeeDetail === void 0 ? void 0 : employeeDetail.work_email)) {
                    throw new exceptions_1.HttpException('Employee doesn\'t exist.', enums_1.HttpStatus.NOT_FOUND);
                }
                const offboardedEmployeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetail(employeeDetail.work_email);
                if (offboardedEmployeeDetail) {
                    throw new exceptions_1.HttpException('The employee exit process has already been initiated.', enums_1.HttpStatus.CONFLICT);
                }
                const entityDetail = yield this.adminApiClient.getBusinessEntityDetailsById(entityId);
                if (!entityDetail) {
                    throw new exceptions_1.HttpException('Entity does not exist.', enums_1.HttpStatus.NOT_FOUND);
                }
                const bsaUserList = yield this.adminApiClient.getUsersByRoleOfAnEntity(enums_1.ROLES.BSA, entityId);
                if (!bsaUserList || bsaUserList.length === 0) {
                    throw new exceptions_1.HttpException('No BSA found for the selected entity.', enums_1.HttpStatus.NOT_FOUND);
                }
                const payload = {
                    employeeDetail: (0, helpers_1.singleObjectToInstance)(dtos_2.BasicEmployeeDetailResponseDto, employeeDetail),
                    entityId: entityDetail.id,
                    entityCode: entityDetail.code,
                    entityTitle: entityDetail.full_name,
                    bsaDetail: (0, helpers_1.multiObjectToInstance)(dtos_3.BusinessEntityRoleUserResponseDto, bsaUserList),
                    lastPhysicalWorkingDate,
                    lastEmploymentDate,
                    isNoticePeriodServed: noticePeriod,
                    noticePeriod: noticeMonths,
                    exitType,
                    note,
                    status: enums_1.OFFBOARDING_STATUS.INITIATED,
                    userStatus: 'Pending Checklist Clearance'
                };
                const newEntry = yield this.offboardedEmployeeDetailRepository.initiateNewExit(payload, currentContext);
                const parentEntityDetail = yield this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, enums_1.HIERARCHY_ENTITY_TYPE.BUSINESS_UNIT);
                if (!parentEntityDetail) {
                    throw new exceptions_1.HttpException('Invalid depatment selected.', enums_1.HttpStatus.NOT_FOUND);
                }
                const generatedApprover = yield this.generateApprovers(newEntry.id, parentEntityDetail.id, currentContext);
                const generatedChecklists = yield this.generateChecklist(newEntry.id, parentEntityDetail.id, currentContext);
                const generatedTask = yield this.generateAllChecklistTask(generatedChecklists, generatedApprover, newEntry, entityId);
                yield this.sendNotificationToChecklistApprovers(generatedTask, generatedChecklists, newEntry);
                yield this.historyApiClient.addRequestHistory({
                    created_by: currentContext.user.username,
                    entity_id: newEntry.id,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                    action_date: new Date(),
                    comments: 'Exit initiated',
                    additional_info: newEntry
                });
                return {
                    message: 'New exit initiated successfully.',
                    data: newEntry
                };
            }));
        });
    }
    getEmployeeExitDetail(id, taskId, currentContext) {
        var _a;
        return __awaiter(this, void 0, void 0, function* () {
            if (taskId) {
                const { taskDetail } = yield this.taskService.validateIfApproverTask(taskId, id, currentContext);
                if (!taskDetail) {
                    throw new exceptions_1.HttpException('Task not found.', enums_1.HttpStatus.BAD_REQUEST);
                }
            }
            const employeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetailById(id);
            if (employeeDetail) {
                const { approvers, checklists } = employeeDetail, exitDetails = __rest(employeeDetail, ["approvers", "checklists"]);
                const groupedChecklist = checklists.reduce((acc, curr) => {
                    acc[curr.groupDisplayName] = acc[curr.groupDisplayName] || [];
                    acc[curr.groupDisplayName].push((0, helpers_1.singleObjectToInstance)(emloyee_complete_detail_response_dto_1.ChecklistResponse, curr));
                    return acc;
                }, {});
                const sortedApprovers = approvers.sort((a, b) => a.approvalSequence - b.approvalSequence);
                let response = {
                    exitDetails,
                    approvers: sortedApprovers,
                    checklists: groupedChecklist
                };
                if (taskId) {
                    response = Object.assign(Object.assign({}, response), { approvalType: ((_a = sortedApprovers.find((approver) => approver.status === enums_1.APPROVER_STATUS.IN_PROGRESS)) === null || _a === void 0 ? void 0 : _a.approvalType) || enums_1.APPROVAL_TYPE.APPROVAL });
                }
                return (0, helpers_1.singleObjectToInstance)(emloyee_complete_detail_response_dto_1.EmployeeDetailResponseDto, response);
            }
            throw new exceptions_1.HttpException('Employee detail does not exist.', enums_1.HttpStatus.NOT_FOUND);
        });
    }
    sendReminder(reminderRequest, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            return {
                message: 'Reminder sent successfully.'
            };
        });
    }
    sendNotificationToChecklistApprovers(generatedTask, generatedChecklists, newEntry) {
        return __awaiter(this, void 0, void 0, function* () {
            const groupedChecklist = generatedChecklists.reduce((acc, curr) => {
                if (!acc[curr.roleKey])
                    acc[curr.roleKey] = [];
                acc[curr.roleKey].push((0, helpers_1.singleObjectToInstance)(emloyee_complete_detail_response_dto_1.ChecklistResponse, curr));
                return acc;
            }, {});
            const groupedTask = generatedTask.reduce((acc, curr) => {
                acc[curr.assigned_to] = curr;
                return acc;
            }, {});
            const { uiClient } = this.configService.getAppConfig();
            const promises = [];
            for (const roleKey in groupedChecklist) {
                const checklists = groupedChecklist[roleKey];
                const checklistsHtml = checklists
                    .map((c) => `<li>${c.checklistTitle}</li>`)
                    .join("");
                const usersPromise = this.getUsersEmailByRoleAndEntityId(roleKey, newEntry.entityId).then((users) => {
                    const task = groupedTask[roleKey];
                    return this.sharedNotificationService.sendNotification(newEntry.id, enums_1.NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK, { to: users }, enums_1.EMAIL_TEMPLATES.CHECKLIST_TASK, {
                        employeeName: newEntry.employeeDetail.fullName,
                        employeeId: newEntry.employeeDetail.personNumber,
                        departmentName: checklists[0].groupDisplayName,
                        checklists: `<ul>${checklistsHtml}</ul>`,
                        taskLink: task
                            ? `<a href="${(0, helpers_1.replaceUrlVariable)(`${uiClient.baseUrl}${types_1.TASK_REL_URL.CHECKLIST_TASK}`, { taskId: task.task_id })}">Submit Checklist</a>`
                            : "",
                    });
                });
                promises.push(usersPromise);
            }
            if (promises.length) {
                yield Promise.all(promises);
            }
        });
    }
    generateApprovers(employeeId, entityId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const masterApprovers = yield this.masterApproverRepository.getEntityMasterApprovers(entityId);
            const approverPayload = masterApprovers.map((approver) => ({
                offboardingEmployeeDetailId: employeeId,
                status: approver.approvalSequence === 1 ? enums_1.APPROVER_STATUS.IN_PROGRESS : enums_1.APPROVER_STATUS.NOT_INITIATED,
                assignedOn: approver.approvalSequence === 1 ? new Date() : null,
                approvalSequence: approver.approvalSequence,
                title: approver.title,
                role: approver.role,
                approvalType: approver.approvalType
            }));
            console.log(approverPayload);
            return yield this.offboardingEmployeeApproverRepository.createExitApprovers(approverPayload, currentContext);
        });
    }
    generateChecklist(employeeId, entityId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const masterChecklists = yield this.masterRoleChecklistRepository.getEntityMasterChecklist(entityId);
            const checklistPayload = masterChecklists.map((checklist) => {
                var _a;
                return ({
                    offboardingEmployeeDetailId: employeeId,
                    checklistTitle: checklist.checklistTitle,
                    groupDisplayName: checklist.groupDisplayName,
                    roleKey: checklist.roleKey,
                    roleType: checklist.roleType,
                    allowedActions: checklist.allowedActions,
                    dependantChecklistCodes: checklist.dependantChecklistCodes,
                    code: checklist.code,
                    sla: checklist.sla,
                    visibilityPermission: checklist.visibilityPermission,
                    assignedOn: ((_a = checklist.dependantChecklistCodes) === null || _a === void 0 ? void 0 : _a.length) ? null : new Date(),
                    uniqueGroupId: checklist.uniqueGroupId
                });
            });
            return yield this.offboardingEmployeeChecklistRepository.createExitChecklists(checklistPayload, currentContext);
        });
    }
    generateAllChecklistTask(checklists, approvers, employeeDetail, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const rolesForTask = [];
            const taskPayloads = [];
            const { uiClient } = this.configService.getAppConfig();
            const checklistApproverDetail = approvers.find((approver) => approver.approvalType === enums_1.APPROVAL_TYPE.CHECKLIST);
            checklists.forEach((checklist) => __awaiter(this, void 0, void 0, function* () {
                if (!rolesForTask.includes(checklist.roleKey)) {
                    taskPayloads.push({
                        title: `${employeeDetail.employeeDetail.fullName} - ${employeeDetail.employeeDetail.personNumber}`,
                        assigned_to: checklist.roleKey,
                        entity_type: types_1.TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK,
                        entity_id: this.taskService.generateTaskEntityId(employeeDetail.id, checklistApproverDetail === null || checklistApproverDetail === void 0 ? void 0 : checklistApproverDetail.id, checklist.uniqueGroupId),
                        is_group_assignemnt: true,
                        business_entity_id: entityId,
                        base_url: uiClient.baseUrl,
                        rel_url: types_1.TASK_REL_URL.CHECKLIST_TASK,
                        additional_info: Object.assign(Object.assign({}, employeeDetail.employeeDetail), { groupDisplayName: checklist.groupDisplayName, exitId: (0, lodash_1.toNumber)(employeeDetail.id), approverId: (0, lodash_1.toNumber)(checklistApproverDetail === null || checklistApproverDetail === void 0 ? void 0 : checklistApproverDetail.id), groupId: (0, lodash_1.toNumber)(checklist.uniqueGroupId), department: checklist.roleKey, entityId: employeeDetail.entityId, entityCode: employeeDetail.entityCode, entityTitle: employeeDetail.entityTitle, lastPhysicalWorkingDate: employeeDetail.lastPhysicalWorkingDate, lastEmploymentDate: employeeDetail.lastEmploymentDate, isNoticePeriodServed: employeeDetail.isNoticePeriodServed, noticePeriod: employeeDetail.noticePeriod, exitType: employeeDetail.exitType })
                    });
                    rolesForTask.push(checklist.roleKey);
                }
            }));
            console.log('taskPayloads', taskPayloads);
            const tasks = yield Promise.all(taskPayloads.map((payload) => this.taskApiClient.createTaskWithUseDelegation(payload)));
            return (0, lodash_1.flatten)(tasks);
        });
    }
    getUsersEmailByRoleAndEntityId(role, entityId) {
        return __awaiter(this, void 0, void 0, function* () {
            const userEmails = [];
            const users = yield this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
            const userIds = users === null || users === void 0 ? void 0 : users.map(user => user.user_name.toLowerCase());
            const userAdDetails = userIds.length
                ? yield this.mSGraphApiClient.getUsersDetails(userIds)
                : [];
            userEmails.push(...userAdDetails.map(user => user.mail));
            return userEmails;
        });
    }
};
EmployeeOffboardingService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [clients_1.AdminApiClient,
        clients_1.EmployeeDataApiClient,
        repositories_1.OffboardedEmployeeDetailRepository,
        helpers_1.DatabaseHelper,
        repositories_1.MasterApproverRepository,
        repositories_1.MasterRoleChecklistRepository,
        repositories_1.OffboardingEmployeeApproverRepository,
        repositories_1.OffboardingEmployeeChecklistRepository,
        clients_1.HistoryApiClient,
        clients_1.MSGraphApiClient,
        config_service_1.ConfigService,
        task_api_client_1.TaskApiClient,
        services_1.TaskService,
        services_2.SharedNotificationService])
], EmployeeOffboardingService);
exports.EmployeeOffboardingService = EmployeeOffboardingService;
//# sourceMappingURL=employee-offboarding.service.js.map