import { Injectable } from "@nestjs/common";
import { BaseRepository } from "src/shared/repositories";
import { OffboardedEmployeeApprover } from "../models";
import { CurrentContext } from "src/shared/types";

@Injectable()
export class OffboardingEmployeeApproverRepository extends BaseRepository<OffboardedEmployeeApprover> {
    constructor() {
        super(OffboardedEmployeeApprover);
    }

    public async createExitApprovers(
        payload: any,
        currentContext: CurrentContext,
    ) {
        return this.insertMany(payload, currentContext);
    }
}