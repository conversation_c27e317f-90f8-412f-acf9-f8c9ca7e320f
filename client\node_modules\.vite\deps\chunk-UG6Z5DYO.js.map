{"version": 3, "sources": ["../../@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"localeText\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '@mui/material/styles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const MuiPickersAdapterContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  MuiPickersAdapterContext.displayName = 'MuiPickersAdapterContext';\n}\n/**\n * Demos:\n *\n * - [Date format and localization](https://mui.com/x/react-date-pickers/adapters-locale/)\n * - [Calendar systems](https://mui.com/x/react-date-pickers/calendar-systems/)\n * - [Translated components](https://mui.com/x/react-date-pickers/localization/)\n * - [UTC and timezones](https://mui.com/x/react-date-pickers/timezone/)\n *\n * API:\n *\n * - [LocalizationProvider API](https://mui.com/x/api/date-pickers/localization-provider/)\n */\nexport const LocalizationProvider = function LocalizationProvider(inProps) {\n  const {\n      localeText: inLocaleText\n    } = inProps,\n    otherInProps = _objectWithoutPropertiesLoose(inProps, _excluded);\n  const {\n    utils: parentUtils,\n    localeText: parentLocaleText\n  } = React.useContext(MuiPickersAdapterContext) ?? {\n    utils: undefined,\n    localeText: undefined\n  };\n  const props = useThemeProps({\n    // We don't want to pass the `localeText` prop to the theme, that way it will always return the theme value,\n    // We will then merge this theme value with our value manually\n    props: otherInProps,\n    name: 'MuiLocalizationProvider'\n  });\n  const {\n    children,\n    dateAdapter: DateAdapter,\n    dateFormats,\n    dateLibInstance,\n    adapterLocale,\n    localeText: themeLocaleText\n  } = props;\n  const localeText = React.useMemo(() => _extends({}, themeLocaleText, parentLocaleText, inLocaleText), [themeLocaleText, parentLocaleText, inLocaleText]);\n  const utils = React.useMemo(() => {\n    if (!DateAdapter) {\n      if (parentUtils) {\n        return parentUtils;\n      }\n      return null;\n    }\n    const adapter = new DateAdapter({\n      locale: adapterLocale,\n      formats: dateFormats,\n      instance: dateLibInstance\n    });\n    if (!adapter.isMUIAdapter) {\n      throw new Error(['MUI X: The date adapter should be imported from `@mui/x-date-pickers` or `@mui/x-date-pickers-pro`, not from `@date-io`', \"For example, `import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'` instead of `import AdapterDayjs from '@date-io/dayjs'`\", 'More information on the installation documentation: https://mui.com/x/react-date-pickers/getting-started/#installation'].join(`\\n`));\n    }\n    return adapter;\n  }, [DateAdapter, adapterLocale, dateFormats, dateLibInstance, parentUtils]);\n  const defaultDates = React.useMemo(() => {\n    if (!utils) {\n      return null;\n    }\n    return {\n      minDate: utils.date('1900-01-01T00:00:00.000'),\n      maxDate: utils.date('2099-12-31T00:00:00.000')\n    };\n  }, [utils]);\n  const contextValue = React.useMemo(() => {\n    return {\n      utils,\n      defaultDates,\n      localeText\n    };\n  }, [defaultDates, utils, localeText]);\n  return /*#__PURE__*/_jsx(MuiPickersAdapterContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n};\nprocess.env.NODE_ENV !== \"production\" ? LocalizationProvider.propTypes = {\n  // ----------------------------- Warning --------------------------------\n  // | These PropTypes are generated from the TypeScript type definitions |\n  // | To update them edit the TypeScript types and run \"pnpm proptypes\"  |\n  // ----------------------------------------------------------------------\n  /**\n   * Locale for the date library you are using\n   */\n  adapterLocale: PropTypes.any,\n  children: PropTypes.node,\n  /**\n   * Date library adapter class function.\n   * @see See the localization provider {@link https://mui.com/x/react-date-pickers/getting-started/#date-library-adapter-setup date adapter setup section} for more details.\n   */\n  dateAdapter: PropTypes.func,\n  /**\n   * Formats that are used for any child pickers\n   */\n  dateFormats: PropTypes.shape({\n    dayOfMonth: PropTypes.string,\n    dayOfMonthFull: PropTypes.string,\n    fullDate: PropTypes.string,\n    fullTime: PropTypes.string,\n    fullTime12h: PropTypes.string,\n    fullTime24h: PropTypes.string,\n    hours12h: PropTypes.string,\n    hours24h: PropTypes.string,\n    keyboardDate: PropTypes.string,\n    keyboardDateTime: PropTypes.string,\n    keyboardDateTime12h: PropTypes.string,\n    keyboardDateTime24h: PropTypes.string,\n    meridiem: PropTypes.string,\n    minutes: PropTypes.string,\n    month: PropTypes.string,\n    monthShort: PropTypes.string,\n    normalDate: PropTypes.string,\n    normalDateWithWeekday: PropTypes.string,\n    seconds: PropTypes.string,\n    shortDate: PropTypes.string,\n    weekday: PropTypes.string,\n    weekdayShort: PropTypes.string,\n    year: PropTypes.string\n  }),\n  /**\n   * Date library instance you are using, if it has some global overrides\n   * ```jsx\n   * dateLibInstance={momentTimeZone}\n   * ```\n   */\n  dateLibInstance: PropTypes.any,\n  /**\n   * Locale for components texts\n   */\n  localeText: PropTypes.object\n} : void 0;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAKA,YAAuB;AACvB,wBAAsB;AAEtB,yBAA4B;AAJ5B,IAAM,YAAY,CAAC,YAAY;AAKxB,IAAM,2BAA8C,oBAAc,IAAI;AAC7E,IAAI,MAAuC;AACzC,2BAAyB,cAAc;AACzC;AAaO,IAAM,uBAAuB,SAASA,sBAAqB,SAAS;AACzE,QAAM;AAAA,IACF,YAAY;AAAA,EACd,IAAI,SACJ,eAAe,8BAA8B,SAAS,SAAS;AACjE,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,YAAY;AAAA,EACd,IAAU,iBAAW,wBAAwB,KAAK;AAAA,IAChD,OAAO;AAAA,IACP,YAAY;AAAA,EACd;AACA,QAAM,QAAQ,cAAc;AAAA;AAAA;AAAA,IAG1B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,YAAY;AAAA,EACd,IAAI;AACJ,QAAM,aAAmB,cAAQ,MAAM,SAAS,CAAC,GAAG,iBAAiB,kBAAkB,YAAY,GAAG,CAAC,iBAAiB,kBAAkB,YAAY,CAAC;AACvJ,QAAM,QAAc,cAAQ,MAAM;AAChC,QAAI,CAAC,aAAa;AAChB,UAAI,aAAa;AACf,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,UAAM,UAAU,IAAI,YAAY;AAAA,MAC9B,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,CAAC,QAAQ,cAAc;AACzB,YAAM,IAAI,MAAM,CAAC,2HAA2H,yIAAyI,wHAAwH,EAAE,KAAK;AAAA,CAAI,CAAC;AAAA,IAC3Z;AACA,WAAO;AAAA,EACT,GAAG,CAAC,aAAa,eAAe,aAAa,iBAAiB,WAAW,CAAC;AAC1E,QAAM,eAAqB,cAAQ,MAAM;AACvC,QAAI,CAAC,OAAO;AACV,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,SAAS,MAAM,KAAK,yBAAyB;AAAA,MAC7C,SAAS,MAAM,KAAK,yBAAyB;AAAA,IAC/C;AAAA,EACF,GAAG,CAAC,KAAK,CAAC;AACV,QAAM,eAAqB,cAAQ,MAAM;AACvC,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF,GAAG,CAAC,cAAc,OAAO,UAAU,CAAC;AACpC,aAAoB,mBAAAC,KAAK,yBAAyB,UAAU;AAAA,IAC1D,OAAO;AAAA,IACP;AAAA,EACF,CAAC;AACH;AACA,OAAwC,qBAAqB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvE,eAAe,kBAAAC,QAAU;AAAA,EACzB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,aAAa,kBAAAA,QAAU,MAAM;AAAA,IAC3B,YAAY,kBAAAA,QAAU;AAAA,IACtB,gBAAgB,kBAAAA,QAAU;AAAA,IAC1B,UAAU,kBAAAA,QAAU;AAAA,IACpB,UAAU,kBAAAA,QAAU;AAAA,IACpB,aAAa,kBAAAA,QAAU;AAAA,IACvB,aAAa,kBAAAA,QAAU;AAAA,IACvB,UAAU,kBAAAA,QAAU;AAAA,IACpB,UAAU,kBAAAA,QAAU;AAAA,IACpB,cAAc,kBAAAA,QAAU;AAAA,IACxB,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,qBAAqB,kBAAAA,QAAU;AAAA,IAC/B,UAAU,kBAAAA,QAAU;AAAA,IACpB,SAAS,kBAAAA,QAAU;AAAA,IACnB,OAAO,kBAAAA,QAAU;AAAA,IACjB,YAAY,kBAAAA,QAAU;AAAA,IACtB,YAAY,kBAAAA,QAAU;AAAA,IACtB,uBAAuB,kBAAAA,QAAU;AAAA,IACjC,SAAS,kBAAAA,QAAU;AAAA,IACnB,WAAW,kBAAAA,QAAU;AAAA,IACrB,SAAS,kBAAAA,QAAU;AAAA,IACnB,cAAc,kBAAAA,QAAU;AAAA,IACxB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,iBAAiB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI3B,YAAY,kBAAAA,QAAU;AACxB,IAAI;", "names": ["LocalizationProvider", "_jsx", "PropTypes"]}