import { AdminApiClient } from 'src/shared/clients';
import { CurrentContext } from 'src/shared/types';
import { BusinessEntityRoleUserResponseDto } from '../dtos';
export declare class BusinessEntityService {
    private readonly adminApiClient;
    constructor(adminApiClient: AdminApiClient);
    getBusinessEntitiesForGivenPermissionForUser(currentContext: CurrentContext, permission?: string, parentId?: number, lastLevel?: string): Promise<Record<string, any>>;
    getBusinessEntitiesChildIds(entities: number[]): Promise<number[]>;
    getAllBusinessEntityLevels(): Promise<Record<string, any>>;
    getAllBusinessEntityRoles(entityLevel: string): Promise<Record<string, any>>;
    getBSAOfSelectedEntity(entityId: number): Promise<BusinessEntityRoleUserResponseDto[]>;
}
