import { EmployeeListResponseDto } from "./paginated-location-list-response.dto";
import { APPROVER_STATUS } from "src/shared/enums";
declare class ApproverResponse {
    id: number;
    title: string;
    isChecklistApprover: boolean;
    role: string;
    approvalSequence: number;
    status: APPROVER_STATUS;
    comment: string;
    assignedOn: Date;
    actionOn: Date;
    actionBy: string;
}
declare class ChecklistResponse {
    id: number;
    checklistTitle: string;
    groupDisplayName: string;
    roleKey: string;
    roleType: string;
    dependantChecklistCodes: string[];
    code: string;
    sla: string;
    assignedOn: Date;
    actionOn: Date;
    actionBy: string;
    actionDetail: any;
    visibilityPermission: string[];
}
export declare class EmployeeDetailResponseDto {
    exitDetails: EmployeeListResponseDto;
    approvers: ApproverResponse[];
    checklists: Record<string, ChecklistResponse[]>;
}
export {};
