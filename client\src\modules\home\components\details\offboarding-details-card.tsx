import SvgColor from '@/components/svg-color';
import { EmployeeExitDetails } from '@/shared/models/employee-exit-response.model';
import { fDate } from '@/shared/utils';
import { ExtentionEnum, getIcon } from '@/shared/utils/get-icon';
import { Box, Card, Chip, Stack, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import React from 'react';
import { EXIT_TYPE_DISPLAY_ENUM } from '../../enums/initiate-offboarding-form.enum';
import {
  EXIT_TYPE_COLOR_ENUM,
  OFFBOARDING_STATUS_COLOR_ENUM,
  OFFBOARDING_STATUS_DISPLAY_ENUM,
} from '../../enums/initiate-offboarding-listing.enum';

type OffboardingDetailsProps = {
  data: EmployeeExitDetails | undefined;
  t: (key: string) => string;
  sx: any;
};

const OffboardingDetailsCard: React.FC<OffboardingDetailsProps> = ({ sx, data, t }) => {
  return (
    <Card
      sx={{
        background: '#FFFFFF',
        boxShadow: '0px 3px 6px #00000029',
        borderRadius: '17px',
        border: '1px solid #E8D6D6',
        px: 3,
        pb: 3,
        pt: 2,
        ...sx,
      }}
    >
      {/* Header */}
      <Stack direction="row" justifyContent="space-between" alignItems="flex-start" pb={2}>
        {/* Title */}
        <Typography variant="mainTitle">{t('headings.offboarding_details')}</Typography>

        {/*  Status on the right */}
        <Stack direction="row" spacing={3}>
          {/* Status */}
          <Stack spacing={0.5}>
            <Typography sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.status')}</Typography>
            <Chip
              size="small"
              sx={{
                fontSize: 12,
                borderRadius: '5px',
                border: '1px solid #89898C',
                color: 'black',
                bgcolor: OFFBOARDING_STATUS_COLOR_ENUM[data?.status as keyof typeof OFFBOARDING_STATUS_COLOR_ENUM],
                '&:hover': {
                  bgcolor: OFFBOARDING_STATUS_COLOR_ENUM[data?.status as keyof typeof OFFBOARDING_STATUS_COLOR_ENUM],
                },
              }}
              label={OFFBOARDING_STATUS_DISPLAY_ENUM[data?.status as keyof typeof OFFBOARDING_STATUS_DISPLAY_ENUM]}
            />
          </Stack>
        </Stack>
      </Stack>

      {/* Row 1: Department + Notice Period + Transferring Group */}
      <Grid container spacing={2} mb={1}>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Stack spacing={0.5}>
            <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.department')}</Box>
            <Box sx={{ fontSize: 16, fontWeight: 500 }}>{data?.entityTitle}</Box>
          </Stack>
        </Grid>

        <Grid size={{ xs: 12, sm: 4 }}>
          <Stack spacing={0.5}>
            <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.notice_period')}</Box>
            <Box sx={{ fontSize: 16, fontWeight: 500 }}>
              {data?.noticePeriod ? `${data.noticePeriod} ${t('label.months')}` : t('label.not_served')}
            </Box>
          </Stack>
        </Grid>

        <Grid size={{ xs: 12, sm: 4 }}>
          <Stack spacing={0.5}>
            <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.exit_type')}</Box>
            <Chip
              size="small"
              sx={{
                fontSize: 12,
                borderRadius: '5px',
                border: '1px solid #89898C',
                width: 100,
                background: EXIT_TYPE_COLOR_ENUM[data?.exitType as keyof typeof EXIT_TYPE_COLOR_ENUM],
              }}
              label={EXIT_TYPE_DISPLAY_ENUM[data?.exitType as keyof typeof EXIT_TYPE_DISPLAY_ENUM]}
            />
          </Stack>
        </Grid>
      </Grid>

      {/* Row 2: Last Physical Date + Last Employment Date + Leaving Group */}
      <Grid container spacing={2} mb={1}>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Stack spacing={0.5}>
            <Box sx={{ fontSize: 14, color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
              <SvgColor sx={{ height: 20, width: 23, mr: 0.5 }} src={getIcon('calendar', ExtentionEnum.PNG)} />
              {t('label.last_physical_working_date')}
            </Box>
            <Box sx={{ fontSize: 16, fontWeight: 500 }}>{fDate(data?.lastPhysicalWorkingDate)}</Box>
          </Stack>
        </Grid>

        <Grid size={{ xs: 12, sm: 4 }}>
          <Stack spacing={0.5}>
            <Box sx={{ fontSize: 14, color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
              <SvgColor sx={{ height: 20, width: 23, mr: 0.5 }} src={getIcon('calendar', ExtentionEnum.PNG)} />
              {t('label.last_employment_date')}
            </Box>
            <Box sx={{ fontSize: 16, fontWeight: 500 }}>{fDate(data?.lastEmploymentDate)}</Box>
          </Stack>
        </Grid>
        {data?.userStatus && ['INITIATED', 'IN_PROGRESS'].includes(data.status) && (
          <Grid size={{ xs: 12, sm: 4 }}>
            <Stack spacing={0.5}>
              <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.pending_with')}</Box>
              <Box sx={{ fontSize: 16, fontWeight: 500 }}>{data?.userStatus}</Box>
            </Stack>
          </Grid>
        )}
      </Grid>

      {/* Row: BSA Detail */}
      {data?.bsaDetail && data?.bsaDetail.length && (
        <Grid container spacing={2} mb={1}>
          <Grid size={{ xs: 12 }}>
            <Stack spacing={0.5}>
              <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.bsa')}</Box>
              <Box sx={{ fontSize: 16, fontWeight: 500 }}>
                {Array.isArray(data?.bsaDetail) && data?.bsaDetail.length > 0
                  ? data?.bsaDetail.map((bsa) => bsa.userName).join(', ')
                  : ''}
              </Box>
            </Stack>
          </Grid>
        </Grid>
      )}

      {/* Row 4: Notes */}
      {data?.note && (
        <Grid container spacing={2}>
          <Grid size={{ xs: 12 }}>
            <Stack spacing={0.5}>
              <Box sx={{ fontSize: 14, color: 'text.secondary' }}>{t('label.additional_notes')}</Box>
              <Box sx={{ fontSize: 16, fontWeight: 500 }}>{data?.note || '-'}</Box>
            </Stack>
          </Grid>
        </Grid>
      )}
    </Card>
  );
};

export default OffboardingDetailsCard;
