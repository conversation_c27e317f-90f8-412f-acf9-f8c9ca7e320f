{"version": 3, "file": "proj.d.ts", "sourceRoot": "", "sources": ["proj.js"], "names": [], "mappings": "AAgIA;;GAEG;AACH,mDAFW,OAAO,QAKjB;AAED;;;;;GAKG;AACH,sCALW,KAAK,CAAC,MAAM,CAAC,WACb,KAAK,CAAC,MAAM,CAAC,GACZ,KAAK,CAAC,MAAM,CAAC,CAaxB;AAED;;;;GAIG;AACH,yCAJW,KAAK,CAAC,MAAM,CAAC,WACb,KAAK,CAAC,MAAM,CAAC,GACZ,KAAK,CAAC,MAAM,CAAC,CAUxB;AAED;;;;;;GAMG;AACH,0CAHW,UAAU,QAMpB;AAED;;GAEG;AACH,4CAFW,KAAK,CAAC,UAAU,CAAC,QAI3B;AAED;;;;;;;;GAQG;AACH,oCANW,cAAc,GAGb,UAAU,GAAC,IAAI,CAkB1B;AAED;;;;;;;;;;;;;;;;;;;GAmBG;AACH,+CARW,cAAc,cACd,MAAM,SACN,OAAO,iBAAiB,EAAE,UAAU,UACpC,OAAO,iBAAiB,EAAE,KAAK,GAE9B,MAAM,CAwDjB;AAED;;;;;;GAMG;AACH,sDAHW,KAAK,CAAC,UAAU,CAAC,QAY3B;AAED;;;;;;;;;;;;GAYG;AACH,sDATW,KAAK,CAAC,UAAU,CAAC,gBAEjB,KAAK,CAAC,UAAU,CAAC,oBAEjB,iBAAiB,oBAEjB,iBAAiB,QAe3B;AAED;;GAEG;AACH,4CAGC;AAED;;;;GAIG;AACH,6CAJW,UAAU,GAAC,MAAM,GAAC,SAAS,eAC3B,MAAM,GACL,UAAU,CAUrB;AAED;;;;;;GAMG;AACH,uEAJW,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,OAAO,iBAAiB,EAAE,UAAU,GAEnF,iBAAiB,CA0B5B;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,gDAfW,cAAc,eACd,cAAc,WACd,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,OAAO,iBAAiB,EAAE,UAAU,WAIpF,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,OAAO,iBAAiB,EAAE,UAAU,QAsB9F;AAED;;;;;;;;GAQG;AACH,uCAPW,OAAO,iBAAiB,EAAE,UAAU,eAEpC,cAAc,GAEb,OAAO,iBAAiB,EAAE,UAAU,CAU/C;AAED;;;;;;;;GAQG;AACH,qCAPW,OAAO,iBAAiB,EAAE,UAAU,eACpC,cAAc,GAEb,OAAO,iBAAiB,EAAE,UAAU,CAe/C;AAED;;;;;;;;;GASG;AACH,wCALW,UAAU,eACV,UAAU,GACT,OAAO,CAalB;AAED;;;;;;;;GAQG;AACH,oDALW,UAAU,eACV,UAAU,GAET,iBAAiB,GAAC,IAAI,CAiEjC;AAcD;;;;;;;;;GASG;AACH,qCALW,cAAc,eACd,cAAc,GACb,iBAAiB,CAO5B;AAED;;;;;;;;;;;;;;;GAeG;AACH,sCANW,OAAO,iBAAiB,EAAE,UAAU,UACpC,cAAc,eACd,cAAc,GACb,OAAO,iBAAiB,EAAE,UAAU,CAa/C;AAED;;;;;;;;;;;GAWG;AACH,wCARW,OAAO,aAAa,EAAE,MAAM,UAC5B,cAAc,eACd,cAAc,UACd,MAAM,GAEL,OAAO,aAAa,EAAE,MAAM,CAMvC;AAED;;;;;;;GAOG;AACH,gDALW,OAAO,iBAAiB,EAAE,UAAU,oBACpC,UAAU,yBACV,UAAU,GACT,OAAO,iBAAiB,EAAE,UAAU,CAY/C;AAOD;;;;;;GAMG;AACH,8CAHW,cAAc,QAKxB;AAED;;;GAGG;AACH,4CAEC;AAED;;;;GAIG;AACH,qCAHY,UAAU,GAAC,IAAI,CAK1B;AAED;;;;;GAKG;AACH,sCAEC;AAED;;;;;;GAMG;AACH,6CAJW,KAAK,CAAC,MAAM,CAAC,oBACb,cAAc,GACb,KAAK,CAAC,MAAM,CAAC,CAOxB;AAED;;;;;;GAMG;AACH,+CAJW,KAAK,CAAC,MAAM,CAAC,kBACb,cAAc,GACb,KAAK,CAAC,MAAM,CAAC,CAoBxB;AAED;;;;;;GAMG;AACH,qCAJW,OAAO,aAAa,EAAE,MAAM,oBAC5B,cAAc,GACb,OAAO,aAAa,EAAE,MAAM,CAOvC;AAED;;;;;;GAMG;AACH,uCAJW,OAAO,aAAa,EAAE,MAAM,kBAC5B,cAAc,GACb,OAAO,aAAa,EAAE,MAAM,CAOvC;AAED;;;;;;;GAOG;AACH,6CAJW,MAAM,oBACN,cAAc,GACb,MAAM,CAWjB;AAED;;;;;;;GAOG;AACH,+CAJW,MAAM,kBACN,cAAc,GACb,MAAM,CAWjB;AAED;;;;;;;;;GASG;AACH,0DALW,UAAU,YACV,UAAU,aACV,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,OAAO,iBAAiB,EAAE,UAAU,GACnF,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,OAAO,iBAAiB,EAAE,UAAU,CA0B/F;AAED;;;;GAIG;AACH,kCAaC;;;;;6BAtwBY,UAAU,GAAC,MAAM,GAAC,SAAS;;;;;aAM1B,iBAAiB;;;;aACjB,iBAAiB;;;;;;;;wCAoBpB,KAAK,CAAC,MAAM,CAAC,iGAIZ,KAAK,CAAC,MAAM,CAAC;uBA1DF,sBAAsB;gCACf,iBAAiB"}