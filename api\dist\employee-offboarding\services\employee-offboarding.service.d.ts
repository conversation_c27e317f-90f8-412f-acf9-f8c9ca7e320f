import { AdminApiClient, EmployeeDataApiClient, HistoryApiClient } from 'src/shared/clients';
import { FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from '../repositories';
export declare class EmployeeOffboardingService {
    private readonly adminApiClient;
    private readonly employeeDataApiClient;
    private readonly offboardedEmployeeDetailRepository;
    private readonly databaseHelper;
    private readonly masterApproverRepository;
    private readonly masterRoleChecklistRepository;
    private readonly offboardingEmployeeApproverRepository;
    private readonly offboardingEmployeeChecklistRepository;
    private readonly historyApiClient;
    constructor(adminApiClient: AdminApiClient, employeeDataApiClient: EmployeeDataApiClient, offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository, databaseHelper: DatabaseHelper, masterApproverRepository: MasterApproverRepository, masterRoleChecklistRepository: MasterRoleChecklistRepository, offboardingEmployeeApproverRepository: OffboardingEmployeeApproverRepository, offboardingEmployeeChecklistRepository: OffboardingEmployeeChecklistRepository, historyApiClient: HistoryApiClient);
    getEmployeeOffboardingList(page?: number, limit?: number, orderBy?: string, orderDirection?: string, filterDto?: FilterRequestDto): Promise<PaginatedEmployeeListResponseDto>;
    initiateNewExit(newExitRequest: InitiateNewExitRequest, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getEmployeeExitDetail(id: number): Promise<any>;
    private generateApprovers;
    private generateChecklist;
}
