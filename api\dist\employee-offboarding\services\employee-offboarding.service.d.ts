import { AdminApiClient, EmployeeDataApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto, ReminderRequestDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { DatabaseHelper } from 'src/shared/helpers';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from '../repositories';
import { ConfigService } from 'src/config/config.service';
import { TaskApiClient } from 'src/shared/clients/task-api.client';
import { TaskService } from 'src/task/services';
import { SharedNotificationService } from 'src/shared/services';
export declare class EmployeeOffboardingService {
    private readonly adminApiClient;
    private readonly employeeDataApiClient;
    private readonly offboardedEmployeeDetailRepository;
    private readonly databaseHelper;
    private readonly masterApproverRepository;
    private readonly masterRoleChecklistRepository;
    private readonly offboardingEmployeeApproverRepository;
    private readonly offboardingEmployeeChecklistRepository;
    private readonly historyApiClient;
    private readonly mSGraphApiClient;
    private readonly configService;
    private readonly taskApiClient;
    private readonly taskService;
    private readonly sharedNotificationService;
    constructor(adminApiClient: AdminApiClient, employeeDataApiClient: EmployeeDataApiClient, offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository, databaseHelper: DatabaseHelper, masterApproverRepository: MasterApproverRepository, masterRoleChecklistRepository: MasterRoleChecklistRepository, offboardingEmployeeApproverRepository: OffboardingEmployeeApproverRepository, offboardingEmployeeChecklistRepository: OffboardingEmployeeChecklistRepository, historyApiClient: HistoryApiClient, mSGraphApiClient: MSGraphApiClient, configService: ConfigService, taskApiClient: TaskApiClient, taskService: TaskService, sharedNotificationService: SharedNotificationService);
    getEmployeeOffboardingList(page?: number, limit?: number, orderBy?: string, orderDirection?: string, filterDto?: FilterRequestDto): Promise<PaginatedEmployeeListResponseDto>;
    initiateNewExit(newExitRequest: InitiateNewExitRequest, currentContext: CurrentContext): Promise<MessageResponseDto>;
    getEmployeeExitDetail(id: number, taskId: number, currentContext: CurrentContext): Promise<any>;
    sendReminder(reminderRequest: ReminderRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private sendNotificationToChecklistApprovers;
    private generateApprovers;
    private generateChecklist;
    private generateAllChecklistTask;
    getUsersEmailByRoleAndEntityId(role: string, entityId: number): Promise<string[]>;
}
