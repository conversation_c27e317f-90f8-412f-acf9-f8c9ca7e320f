import CustomModal from '@/components/custom-modal/custom-modal';
import { RHFTextField } from '@/components/hook-form';
import CustomUpload from '@/components/upload/custom-upload';
import { handleDrop, handleRemoveFile } from '@/components/upload/custom-upload-utils';
import { useTranslate } from '@/locales/use-locales';
import { Button, MenuItem, Stack, Typography } from '@mui/material';
import { Box } from '@mui/system';
import React, { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { CloseIcon } from 'yet-another-react-lightbox';

export enum ALLOWED_ACTION_TYPE {
  AMOUNT = 'AMOUNT',
  OPTIONS = 'OPTIONS',
  ATTACHMENT = 'ATTACHMENT',
  NOTE = 'NOTE',
  INPUT = 'INPUT',
}

export const INPUT_TYPE = {
  NUMBER: 'NUMBER',
  STRING: 'STRING',
  DATE: 'DATE',
  SELECT: 'SELECT',
  DOCUMENT: 'DOCUMENT',
  TEXTAREA: 'TEXTAREA',
};

interface FieldConfig {
  inputType?: string;
  label: string;
  isRequired?: boolean;
  info?: string;
  optionList?: string[];
  type: any;
}

interface SubmitActionDialogProps {
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
  allowedConfig: FieldConfig[];
  task?: any;
}

export const SubmitActionDialog: React.FC<SubmitActionDialogProps> = ({
  open,
  onClose,
  onSubmit,
  allowedConfig,
  task,
}) => {
  const { t } = useTranslate();
  const { control, watch, setValue, reset } = useFormContext();
  useEffect(() => {
    if (open) {
      reset({});
    }
  }, [open, reset]);
  return (
    <CustomModal isOpen={open} handleClose={onClose}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="mainTitle" component="h2">
          {task?.checklistTitle}
        </Typography>
        <div onClick={onClose} style={{ cursor: 'pointer' }}>
          <CloseIcon />
        </div>
      </Box>

      {/* Scrollable Content */}
      <Box
        sx={{
          maxHeight: '65vh',
          overflowY: 'auto',
          pr: 1,
          pt: 1,
        }}
      >
        <Stack spacing={1}>
          {allowedConfig.map((field, index) => {
            const labelWithAsterisk = field.isRequired ? `${field.label} *` : field.label;

            return (
              <Controller
                key={index}
                name={field.type}
                control={control}
                defaultValue={field.type === ALLOWED_ACTION_TYPE.OPTIONS ? '' : ''}
                rules={{ required: field.isRequired ? `${field.label} is required` : false }}
                render={({ field: rhfField, fieldState: { error } }) => {
                  switch (field.type) {
                    case ALLOWED_ACTION_TYPE.AMOUNT:
                      return (
                        <RHFTextField
                          {...rhfField}
                          label={labelWithAsterisk}
                          type="number"
                          helperText={error ? error.message : field.info}
                          error={!!error}
                          fullWidth
                        />
                      );

                    case ALLOWED_ACTION_TYPE.OPTIONS:
                      return (
                        <RHFTextField
                          {...rhfField}
                          select
                          label={labelWithAsterisk}
                          error={!!error}
                          value={rhfField.value ?? ''}
                          helperText={error ? error.message : field.info}
                          fullWidth
                        >
                          {field.optionList?.map((opt, i) => (
                            <MenuItem key={i} value={opt}>
                              {opt}
                            </MenuItem>
                          ))}
                        </RHFTextField>
                      );

                    case ALLOWED_ACTION_TYPE.NOTE:
                      return (
                        <RHFTextField
                          {...rhfField}
                          label={labelWithAsterisk}
                          multiline
                          rows={3}
                          helperText={error ? error.message : field.info}
                          error={!!error}
                          fullWidth
                        />
                      );

                    case ALLOWED_ACTION_TYPE.INPUT:
                      return (
                        <RHFTextField
                          {...rhfField}
                          label={labelWithAsterisk}
                          type="text"
                          helperText={error ? error.message : field.info}
                          error={!!error}
                          fullWidth
                        />
                      );

                    case ALLOWED_ACTION_TYPE.ATTACHMENT:
                      return (
                        <CustomUpload
                          multiple
                          trimTextNumber={30}
                          uploadFilesTitle={t('label.documents_upload')}
                          previewFilesTitle={t('label.uploaded_files')}
                          files={watch(field.type)}
                          onDrop={(files) => handleDrop(field.type, files, { setValue, watch }, t)}
                          onRemove={(file) => handleRemoveFile(field.type, file, { setValue, watch })}
                        />
                      );

                    default:
                      return <></>;
                  }
                }}
              />
            );
          })}

          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px', marginTop: '10px' }}>
            <Button onClick={onClose}>{t('btn_name.cancel')}</Button>
            <Button variant="contained" onClick={onSubmit}>
              {t('btn_name.submit')}
            </Button>
          </div>
        </Stack>
      </Box>

      {/* Fixed Actions */}
      {/* <DialogActions sx={{ p: 0 }}>
        <Button onClick={onClose}>{t('btn_name.cancel')}</Button>
        <Button variant="contained" onClick={onSubmit}>
          {t('btn_name.submit')}
        </Button>
      </DialogActions> */}
    </CustomModal>
  );
};

export default SubmitActionDialog;
