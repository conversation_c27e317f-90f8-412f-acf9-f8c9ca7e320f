{"version": 3, "sources": ["../../@mui/material/Grow/Grow.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useTimeout from '@mui/utils/useTimeout';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport { Transition } from 'react-transition-group';\nimport { useTheme } from \"../zero-styled/index.js\";\nimport { getTransitionProps, reflow } from \"../transitions/utils.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction getScale(value) {\n  return `scale(${value}, ${value ** 2})`;\n}\nconst styles = {\n  entering: {\n    opacity: 1,\n    transform: getScale(1)\n  },\n  entered: {\n    opacity: 1,\n    transform: 'none'\n  }\n};\n\n/*\n TODO v6: remove\n Conditionally apply a workaround for the CSS transition bug in Safari 15.4 / WebKit browsers.\n */\nconst isWebKit154 = typeof navigator !== 'undefined' && /^((?!chrome|android).)*(safari|mobile)/i.test(navigator.userAgent) && /(os |version\\/)15(.|_)4/i.test(navigator.userAgent);\n\n/**\n * The Grow transition is used by the [Tooltip](/material-ui/react-tooltip/) and\n * [Popover](/material-ui/react-popover/) components.\n * It uses [react-transition-group](https://github.com/reactjs/react-transition-group) internally.\n */\nconst Grow = /*#__PURE__*/React.forwardRef(function Grow(props, ref) {\n  const {\n    addEndListener,\n    appear = true,\n    children,\n    easing,\n    in: inProp,\n    onEnter,\n    onEntered,\n    onEntering,\n    onExit,\n    onExited,\n    onExiting,\n    style,\n    timeout = 'auto',\n    // eslint-disable-next-line react/prop-types\n    TransitionComponent = Transition,\n    ...other\n  } = props;\n  const timer = useTimeout();\n  const autoTimeout = React.useRef();\n  const theme = useTheme();\n  const nodeRef = React.useRef(null);\n  const handleRef = useForkRef(nodeRef, getReactElementRef(children), ref);\n  const normalizedTransitionCallback = callback => maybeIsAppearing => {\n    if (callback) {\n      const node = nodeRef.current;\n\n      // onEnterXxx and onExitXxx callbacks have a different arguments.length value.\n      if (maybeIsAppearing === undefined) {\n        callback(node);\n      } else {\n        callback(node, maybeIsAppearing);\n      }\n    }\n  };\n  const handleEntering = normalizedTransitionCallback(onEntering);\n  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {\n    reflow(node); // So the animation always start from the start.\n\n    const {\n      duration: transitionDuration,\n      delay,\n      easing: transitionTimingFunction\n    } = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'enter'\n    });\n    let duration;\n    if (timeout === 'auto') {\n      duration = theme.transitions.getAutoHeightDuration(node.clientHeight);\n      autoTimeout.current = duration;\n    } else {\n      duration = transitionDuration;\n    }\n    node.style.transition = [theme.transitions.create('opacity', {\n      duration,\n      delay\n    }), theme.transitions.create('transform', {\n      duration: isWebKit154 ? duration : duration * 0.666,\n      delay,\n      easing: transitionTimingFunction\n    })].join(',');\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  });\n  const handleEntered = normalizedTransitionCallback(onEntered);\n  const handleExiting = normalizedTransitionCallback(onExiting);\n  const handleExit = normalizedTransitionCallback(node => {\n    const {\n      duration: transitionDuration,\n      delay,\n      easing: transitionTimingFunction\n    } = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'exit'\n    });\n    let duration;\n    if (timeout === 'auto') {\n      duration = theme.transitions.getAutoHeightDuration(node.clientHeight);\n      autoTimeout.current = duration;\n    } else {\n      duration = transitionDuration;\n    }\n    node.style.transition = [theme.transitions.create('opacity', {\n      duration,\n      delay\n    }), theme.transitions.create('transform', {\n      duration: isWebKit154 ? duration : duration * 0.666,\n      delay: isWebKit154 ? delay : delay || duration * 0.333,\n      easing: transitionTimingFunction\n    })].join(',');\n    node.style.opacity = 0;\n    node.style.transform = getScale(0.75);\n    if (onExit) {\n      onExit(node);\n    }\n  });\n  const handleExited = normalizedTransitionCallback(onExited);\n  const handleAddEndListener = next => {\n    if (timeout === 'auto') {\n      timer.start(autoTimeout.current || 0, next);\n    }\n    if (addEndListener) {\n      // Old call signature before `react-transition-group` implemented `nodeRef`\n      addEndListener(nodeRef.current, next);\n    }\n  };\n  return /*#__PURE__*/_jsx(TransitionComponent, {\n    appear: appear,\n    in: inProp,\n    nodeRef: nodeRef,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    timeout: timeout === 'auto' ? null : timeout,\n    ...other,\n    children: (state, {\n      ownerState,\n      ...restChildProps\n    }) => {\n      return /*#__PURE__*/React.cloneElement(children, {\n        style: {\n          opacity: 0,\n          transform: getScale(0.75),\n          visibility: state === 'exited' && !inProp ? 'hidden' : undefined,\n          ...styles[state],\n          ...style,\n          ...children.props.style\n        },\n        ref: handleRef,\n        ...restChildProps\n      });\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Grow.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Add a custom transition end trigger. Called with the transitioning DOM\n   * node and a done callback. Allows for more fine grained transition end\n   * logic. Note: Timeouts are still used as a fallback if provided.\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Perform the enter transition when it first mounts if `in` is also `true`.\n   * Set this to `false` to disable this behavior.\n   * @default true\n   */\n  appear: PropTypes.bool,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * The transition timing function.\n   * You may specify a single easing or a object containing enter and exit values.\n   */\n  easing: PropTypes.oneOfType([PropTypes.shape({\n    enter: PropTypes.string,\n    exit: PropTypes.string\n  }), PropTypes.string]),\n  /**\n   * If `true`, the component will transition in.\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntered: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntering: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExit: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExited: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExiting: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   *\n   * Set to 'auto' to automatically calculate transition time based on height.\n   * @default 'auto'\n   */\n  timeout: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nif (Grow) {\n  Grow.muiSupportAuto = true;\n}\nexport default Grow;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;AAQtB,yBAA4B;AAC5B,SAAS,SAAS,OAAO;AACvB,SAAO,SAAS,KAAK,KAAK,SAAS,CAAC;AACtC;AACA,IAAM,SAAS;AAAA,EACb,UAAU;AAAA,IACR,SAAS;AAAA,IACT,WAAW,SAAS,CAAC;AAAA,EACvB;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF;AAMA,IAAM,cAAc,OAAO,cAAc,eAAe,0CAA0C,KAAK,UAAU,SAAS,KAAK,2BAA2B,KAAK,UAAU,SAAS;AAOlL,IAAM,OAA0B,iBAAW,SAASA,MAAK,OAAO,KAAK;AACnE,QAAM;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,IACtB,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,QAAQ,WAAW;AACzB,QAAM,cAAoB,aAAO;AACjC,QAAM,QAAQ,SAAS;AACvB,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,YAAY,mBAAW,SAAS,mBAAmB,QAAQ,GAAG,GAAG;AACvE,QAAM,+BAA+B,cAAY,sBAAoB;AACnE,QAAI,UAAU;AACZ,YAAM,OAAO,QAAQ;AAGrB,UAAI,qBAAqB,QAAW;AAClC,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,iBAAS,MAAM,gBAAgB;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,6BAA6B,UAAU;AAC9D,QAAM,cAAc,6BAA6B,CAAC,MAAM,gBAAgB;AACtE,WAAO,IAAI;AAEX,UAAM;AAAA,MACJ,UAAU;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACV,IAAI,mBAAmB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,QAAI;AACJ,QAAI,YAAY,QAAQ;AACtB,iBAAW,MAAM,YAAY,sBAAsB,KAAK,YAAY;AACpE,kBAAY,UAAU;AAAA,IACxB,OAAO;AACL,iBAAW;AAAA,IACb;AACA,SAAK,MAAM,aAAa,CAAC,MAAM,YAAY,OAAO,WAAW;AAAA,MAC3D;AAAA,MACA;AAAA,IACF,CAAC,GAAG,MAAM,YAAY,OAAO,aAAa;AAAA,MACxC,UAAU,cAAc,WAAW,WAAW;AAAA,MAC9C;AAAA,MACA,QAAQ;AAAA,IACV,CAAC,CAAC,EAAE,KAAK,GAAG;AACZ,QAAI,SAAS;AACX,cAAQ,MAAM,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,aAAa,6BAA6B,UAAQ;AACtD,UAAM;AAAA,MACJ,UAAU;AAAA,MACV;AAAA,MACA,QAAQ;AAAA,IACV,IAAI,mBAAmB;AAAA,MACrB;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,QAAI;AACJ,QAAI,YAAY,QAAQ;AACtB,iBAAW,MAAM,YAAY,sBAAsB,KAAK,YAAY;AACpE,kBAAY,UAAU;AAAA,IACxB,OAAO;AACL,iBAAW;AAAA,IACb;AACA,SAAK,MAAM,aAAa,CAAC,MAAM,YAAY,OAAO,WAAW;AAAA,MAC3D;AAAA,MACA;AAAA,IACF,CAAC,GAAG,MAAM,YAAY,OAAO,aAAa;AAAA,MACxC,UAAU,cAAc,WAAW,WAAW;AAAA,MAC9C,OAAO,cAAc,QAAQ,SAAS,WAAW;AAAA,MACjD,QAAQ;AAAA,IACV,CAAC,CAAC,EAAE,KAAK,GAAG;AACZ,SAAK,MAAM,UAAU;AACrB,SAAK,MAAM,YAAY,SAAS,IAAI;AACpC,QAAI,QAAQ;AACV,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,eAAe,6BAA6B,QAAQ;AAC1D,QAAM,uBAAuB,UAAQ;AACnC,QAAI,YAAY,QAAQ;AACtB,YAAM,MAAM,YAAY,WAAW,GAAG,IAAI;AAAA,IAC5C;AACA,QAAI,gBAAgB;AAElB,qBAAe,QAAQ,SAAS,IAAI;AAAA,IACtC;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,qBAAqB;AAAA,IAC5C;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,SAAS,YAAY,SAAS,OAAO;AAAA,IACrC,GAAG;AAAA,IACH,UAAU,CAAC,OAAO;AAAA,MAChB;AAAA,MACA,GAAG;AAAA,IACL,MAAM;AACJ,aAA0B,mBAAa,UAAU;AAAA,QAC/C,OAAO;AAAA,UACL,SAAS;AAAA,UACT,WAAW,SAAS,IAAI;AAAA,UACxB,YAAY,UAAU,YAAY,CAAC,SAAS,WAAW;AAAA,UACvD,GAAG,OAAO,KAAK;AAAA,UACf,GAAG;AAAA,UACH,GAAG,SAAS,MAAM;AAAA,QACpB;AAAA,QACA,KAAK;AAAA,QACL,GAAG;AAAA,MACL,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9E,gBAAgB,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,4BAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM;AAAA,IAC3C,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,MAAM,CAAC,GAAG,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IACzF,QAAQ,kBAAAA,QAAU;AAAA,IAClB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,CAAC,CAAC;AACL,IAAI;AACJ,IAAI,MAAM;AACR,OAAK,iBAAiB;AACxB;AACA,IAAO,eAAQ;", "names": ["Grow", "_jsx", "PropTypes"]}