"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffboardedEmployeeDetailRepository = void 0;
const common_1 = require("@nestjs/common");
const repositories_1 = require("../../shared/repositories");
const models_1 = require("../models");
const sequelize_1 = require("sequelize");
let OffboardedEmployeeDetailRepository = class OffboardedEmployeeDetailRepository extends repositories_1.BaseRepository {
    constructor() {
        super(models_1.OffboardedEmployeeDetail);
    }
    initiateNewExit(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const newExit = new models_1.OffboardedEmployeeDetail(payload);
            return this.save(newExit, currentContext);
        });
    }
    getOffboardedEmployeeDetail(employeeEmailId) {
        return __awaiter(this, void 0, void 0, function* () {
            const condition = {
                where: {
                    employeeDetail: {
                        [sequelize_1.Op.contains]: { workEmail: employeeEmailId.toLowerCase() }
                    }
                }
            };
            return this.findOne(condition);
        });
    }
    getOffboardedEmployeeDetailById(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findOne({
                where: {
                    id
                },
                include: [
                    {
                        model: models_1.OffboardedEmployeeApprover,
                        as: 'approvers',
                        where: {
                            active: true,
                            deleted: false
                        }
                    },
                    {
                        model: models_1.OffboardedEmployeeChecklist,
                        as: 'checklists',
                        where: {
                            active: true,
                            deleted: false
                        }
                    }
                ]
            });
        });
    }
    getEmployeeOffboardingListByFilter(filterDto, page, limit, orderBy, orderDirection) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.findAndCountAll(Object.assign(Object.assign(Object.assign({}, (page && { offset: (page - 1) * limit })), (limit && { limit })), { order: [[this.getOrderByColumn(orderBy), this.getOrderByDirection(orderDirection)]], distinct: true }));
        });
    }
    getOrderByColumn(orderBy) {
        switch (orderBy) {
            case 'status':
                return 'status';
            default:
                return 'updatedOn';
        }
    }
    getOrderByDirection(orderByDirection) {
        switch (orderByDirection) {
            case 'asc':
            case 'ASC':
                return 'ASC';
            case 'desc':
            case 'DESC':
                return 'DESC';
            default:
                return 'DESC';
        }
    }
};
OffboardedEmployeeDetailRepository = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [])
], OffboardedEmployeeDetailRepository);
exports.OffboardedEmployeeDetailRepository = OffboardedEmployeeDetailRepository;
//# sourceMappingURL=offboarded-employees-detail.repository.js.map