import ScrollToTopButton from '@/components/scroll-to-top';
import { useTranslate } from '@/locales/use-locales';
import { useSearchParams } from '@/routes/hooks';
import { EmployeeExitResponse } from '@/shared/models/employee-exit-response.model';
import { Autocomplete, Box, Card, TextField, Typography } from '@mui/material';
import Grid from '@mui/material/Grid2';
import { Stack } from '@mui/system';
import { useRef, useState } from 'react';
import ApproverAction from './approver-action';
import ApprovalTimeline from './approver-list';
import EmployeeDetail from './employee-details';
import OffboardingChecklistAccordion from './offboarding-checklist';
import OffboardingDetailsCard from './offboarding-details-card';
import PaymentSummary from './payment-summary';
import HistoryContent from './view-history';

interface Props {
  employeeExitDetail: EmployeeExitResponse | undefined;
}

const EmployeeOffboardingDetails = ({ employeeExitDetail }: Props) => {
  const employeeDetails = employeeExitDetail?.exitDetails?.employeeDetail;
  const offboardingDetails = employeeExitDetail?.exitDetails;
  const approverList = employeeExitDetail?.approvers ?? [];
  const checkLists = employeeExitDetail?.checklists ?? {};
  const approvalType = employeeExitDetail?.approvalType ?? null;
  const exitId = employeeExitDetail?.exitDetails.id ?? '';
  const searchParams = useSearchParams();
  const taskId = searchParams.get('taskId') ?? '';
  const { t } = useTranslate();

  const [expanded, setExpanded] = useState<string | false>(false);
  const taskRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const groupedChecklists = Object.entries(checkLists).map(([title, tasks]) => ({
    title,
    tasks,
  }));

  // Autocomplete dropdown options
  const taskOptions = groupedChecklists.flatMap((section) =>
    section.tasks.map((task: any) => ({
      id: task.id,
      label: task.checklistTitle,
      sectionTitle: section.title,
      ...task,
    })),
  );

  const handleTaskSelect = (_: any, value: any) => {
    if (!value) return;

    // Expand the correct accordion section
    setExpanded(value.sectionTitle);

    // Scroll to the task
    setTimeout(() => {
      const refKey = `${value.sectionTitle}-${value.id}`;
      taskRefs.current[refKey]?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 300);
  };

  return (
    <Grid container spacing={2} mt={2} px={{ md: 2 }} width="100%">
      {/* First Row */}
      <Grid container spacing={2} width="100%" alignItems="stretch">
        <Grid size={{ xs: 12, lg: 8 }} sx={{ display: 'flex' }}>
          <Box sx={{ flex: 1, display: 'flex' }}>
            <OffboardingDetailsCard data={offboardingDetails} t={t} sx={{ flex: 1 }} />
          </Box>
        </Grid>
        <Grid size={{ xs: 12, lg: 4 }} sx={{ display: 'flex' }}>
          <Box sx={{ flex: 1, display: 'flex' }}>
            <EmployeeDetail employeeDetails={employeeDetails} sx={{ flex: 1 }} />
          </Box>
        </Grid>
      </Grid>

      {/* Second Row */}
      <Grid container spacing={2} width="100%">
        <Grid size={{ xs: 12, lg: 8 }}>
          <Card
            sx={{
              background: '#FFFFFF',
              boxShadow: '0px 3px 6px #00000029',
              borderRadius: '17px',
              border: '1px solid #E8D6D6',
              px: 2,
              py: 2,
            }}
          >
            {/* Search bar */}
            <Stack pb={2} direction="row" justifyContent="space-between" alignItems="center">
              <Typography variant="mainTitle">{t('headings.employee_checklist')}</Typography>
              <Autocomplete
                size="small"
                sx={{ width: 400 }}
                options={taskOptions}
                disablePortal
                noOptionsText={
                  <Typography sx={{ textAlign: 'center', color: 'text.secondary' }}>
                    {t('messages.no_checklist_available')}
                  </Typography>
                }
                getOptionLabel={(option) => option.label}
                onChange={handleTaskSelect}
                renderInput={(params) => <TextField {...params} label={t('label.search_task')} variant="outlined" />}
              />
            </Stack>

            {/* Accordions */}
            <Grid>
              {groupedChecklists.map((section, idx) => (
                <Box key={idx} mb={2}>
                  <OffboardingChecklistAccordion
                    title={section.title}
                    exitId={Number(exitId)}
                    expanded={expanded === section.title}
                    onChange={() => setExpanded(expanded === section.title ? false : section.title)}
                    checklistData={section.tasks.map((task: any) => ({
                      ...task,
                      taskOptions,
                    }))}
                    taskRefs={taskRefs}
                  />
                </Box>
              ))}
            </Grid>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, lg: 4 }}>
          <Stack spacing={2}>
            {taskId && <ApproverAction exitId={exitId} taskId={taskId} approvalType={approvalType} />}
            <PaymentSummary t={t} payments={1000} deductions={500} netSettlement={500} />
            <ApprovalTimeline approvers={approverList} />
            <HistoryContent exitId={exitId} />
          </Stack>
        </Grid>
      </Grid>

      <ScrollToTopButton />
    </Grid>
  );
};

export default EmployeeOffboardingDetails;
