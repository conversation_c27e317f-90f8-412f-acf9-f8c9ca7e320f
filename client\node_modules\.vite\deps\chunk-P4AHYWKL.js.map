{"version": 3, "sources": ["../../@mui/material/Table/Table.js", "../../@mui/material/Table/tableClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport TableContext from \"./TableContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getTableUtilityClass } from \"./tableClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    stickyHeader\n  } = ownerState;\n  const slots = {\n    root: ['root', stickyHeader && 'stickyHeader']\n  };\n  return composeClasses(slots, getTableUtilityClass, classes);\n};\nconst TableRoot = styled('table', {\n  name: 'MuiTable',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.stickyHeader && styles.stickyHeader];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'table',\n  width: '100%',\n  borderCollapse: 'collapse',\n  borderSpacing: 0,\n  '& caption': {\n    ...theme.typography.body2,\n    padding: theme.spacing(2),\n    color: (theme.vars || theme).palette.text.secondary,\n    textAlign: 'left',\n    captionSide: 'bottom'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.stickyHeader,\n    style: {\n      borderCollapse: 'separate'\n    }\n  }]\n})));\nconst defaultComponent = 'table';\nconst Table = /*#__PURE__*/React.forwardRef(function Table(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiTable'\n  });\n  const {\n    className,\n    component = defaultComponent,\n    padding = 'normal',\n    size = 'medium',\n    stickyHeader = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component,\n    padding,\n    size,\n    stickyHeader\n  };\n  const classes = useUtilityClasses(ownerState);\n  const table = React.useMemo(() => ({\n    padding,\n    size,\n    stickyHeader\n  }), [padding, size, stickyHeader]);\n  return /*#__PURE__*/_jsx(TableContext.Provider, {\n    value: table,\n    children: /*#__PURE__*/_jsx(TableRoot, {\n      as: component,\n      role: component === defaultComponent ? null : 'table',\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState,\n      ...other\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Table.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the table, normally `TableHead` and `TableBody`.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Allows TableCells to inherit padding of the Table.\n   * @default 'normal'\n   */\n  padding: PropTypes.oneOf(['checkbox', 'none', 'normal']),\n  /**\n   * Allows TableCells to inherit size of the Table.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * Set the header sticky.\n   * @default false\n   */\n  stickyHeader: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Table;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTableUtilityClass(slot) {\n  return generateUtilityClass('MuiTable', slot);\n}\nconst tableClasses = generateUtilityClasses('MuiTable', ['root', 'stickyHeader']);\nexport default tableClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AACvB,wBAAsB;;;ACDf,SAAS,qBAAqB,MAAM;AACzC,SAAO,qBAAqB,YAAY,IAAI;AAC9C;AACA,IAAM,eAAe,uBAAuB,YAAY,CAAC,QAAQ,cAAc,CAAC;AAChF,IAAO,uBAAQ;;;ADKf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,gBAAgB,cAAc;AAAA,EAC/C;AACA,SAAO,eAAe,OAAO,sBAAsB,OAAO;AAC5D;AACA,IAAM,YAAY,eAAO,SAAS;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,gBAAgB,OAAO,YAAY;AAAA,EACrE;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,aAAa;AAAA,IACX,GAAG,MAAM,WAAW;AAAA,IACpB,SAAS,MAAM,QAAQ,CAAC;AAAA,IACxB,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC1C,WAAW;AAAA,IACX,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,MACN;AAAA,IACF,MAAM,WAAW;AAAA,IACjB,OAAO;AAAA,MACL,gBAAgB;AAAA,IAClB;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,mBAAmB;AACzB,IAAM,QAA2B,iBAAW,SAASA,OAAM,SAAS,KAAK;AACvE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,eAAe;AAAA,IACf,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,QAAc,cAAQ,OAAO;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,CAAC,SAAS,MAAM,YAAY,CAAC;AACjC,aAAoB,mBAAAC,KAAK,qBAAa,UAAU;AAAA,IAC9C,OAAO;AAAA,IACP,cAAuB,mBAAAA,KAAK,WAAW;AAAA,MACrC,IAAI;AAAA,MACJ,MAAM,cAAc,mBAAmB,OAAO;AAAA,MAC9C;AAAA,MACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,MACvC;AAAA,MACA,GAAG;AAAA,IACL,CAAC;AAAA,EACH,CAAC;AACH,CAAC;AACD,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/E,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,SAAS,kBAAAA,QAAU,MAAM,CAAC,YAAY,QAAQ,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,MAAM,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxH,cAAc,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIxB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,gBAAQ;", "names": ["Table", "_jsx", "PropTypes"]}