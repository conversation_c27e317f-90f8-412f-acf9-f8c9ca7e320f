"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmployeeOffboardingController = void 0;
const common_1 = require("@nestjs/common");
const passport_1 = require("@nestjs/passport");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../services");
const enums_1 = require("../../shared/enums");
const guards_1 = require("../../core/guards");
const dtos_1 = require("../../shared/dtos");
const decorators_1 = require("../../core/decorators");
const dtos_2 = require("../dtos");
let EmployeeOffboardingController = class EmployeeOffboardingController {
    constructor(employeeOffboardingService) {
        this.employeeOffboardingService = employeeOffboardingService;
    }
    initiateNewExit(request, newExitRequest) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.employeeOffboardingService.initiateNewExit(newExitRequest, request.currentContext);
        });
    }
    getEmployeeOffboardingList(page = 1, limit = 10, orderBy = 'updatedOn', orderDirection = 'DESC', filterDto) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.employeeOffboardingService.getEmployeeOffboardingList(page, limit, orderBy, orderDirection, filterDto);
        });
    }
    getEmployeeExitDetail(id) {
        return __awaiter(this, void 0, void 0, function* () {
            return this.employeeOffboardingService.getEmployeeExitDetail(id);
        });
    }
};
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.APPLICATION_ADMIN, { checkEntity: true }),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Initiate new employee exit.',
        type: dtos_1.MessageResponseDto,
    }),
    (0, common_1.Post)('/initiate-exit'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dtos_2.InitiateNewExitRequest]),
    __metadata("design:returntype", Promise)
], EmployeeOffboardingController.prototype, "initiateNewExit", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.APPLICATION_ADMIN),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get list of paginated employee list by filter criteria.',
        type: dtos_2.PaginatedEmployeeListResponseDto,
    }),
    (0, common_1.Post)('/exit-list'),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('orderBy')),
    __param(3, (0, common_1.Query)('orderDirection')),
    __param(4, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String, dtos_2.FilterRequestDto]),
    __metadata("design:returntype", Promise)
], EmployeeOffboardingController.prototype, "getEmployeeOffboardingList", null);
__decorate([
    (0, decorators_1.Permissions)(enums_1.PERMISSIONS.APPLICATION_ADMIN),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer'), guards_1.PermissionsGuard),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Get employee exit complete detail.',
    }),
    (0, common_1.Get)('/exit-detail/:id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], EmployeeOffboardingController.prototype, "getEmployeeExitDetail", null);
EmployeeOffboardingController = __decorate([
    (0, swagger_1.ApiTags)('Employee Offboarding APIs'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)((0, passport_1.AuthGuard)('oauth-bearer')),
    (0, common_1.Controller)('employee-offboarding'),
    __metadata("design:paramtypes", [services_1.EmployeeOffboardingService])
], EmployeeOffboardingController);
exports.EmployeeOffboardingController = EmployeeOffboardingController;
//# sourceMappingURL=employee-offboarding.controller.js.map