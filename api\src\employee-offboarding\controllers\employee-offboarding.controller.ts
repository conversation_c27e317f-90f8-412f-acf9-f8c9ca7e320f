import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RequestContext } from 'src/shared/types';
import { EmployeeOffboardingService } from '../services';
import { PERMISSIONS } from 'src/shared/enums';
import { PermissionsGuard } from 'src/core/guards';
import { MessageResponseDto } from 'src/shared/dtos';
import { Permissions } from 'src/core/decorators';
import { FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto } from '../dtos';

@ApiTags('Employee Offboarding APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('employee-offboarding')
export class EmployeeOffboardingController {
    constructor(private readonly employeeOffboardingService: EmployeeOffboardingService) { }

    @Permissions(PERMISSIONS.APPLICATION_ADMIN, { checkEntity: true })
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Initiate new employee exit.',
        type: MessageResponseDto,
    })
    @Post('/initiate-exit')
    public async initiateNewExit(
        @Req() request: RequestContext,
        @Body() newExitRequest: InitiateNewExitRequest,
    ): Promise<MessageResponseDto> {
        return this.employeeOffboardingService.initiateNewExit(newExitRequest, request.currentContext);
    }

    @Permissions(PERMISSIONS.APPLICATION_ADMIN)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get list of paginated employee list by filter criteria.',
        type: PaginatedEmployeeListResponseDto,
    })
    @Post('/exit-list')
    public async getEmployeeOffboardingList(
        @Query('page') page: number = 1,
        @Query('limit') limit: number = 10,
        @Query('orderBy') orderBy: string = 'updatedOn',
        @Query('orderDirection') orderDirection: string = 'DESC',
        @Body() filterDto?: FilterRequestDto,
    ): Promise<PaginatedEmployeeListResponseDto> {
        return this.employeeOffboardingService.getEmployeeOffboardingList(page, limit, orderBy, orderDirection, filterDto);
    }

    @Permissions(PERMISSIONS.APPLICATION_ADMIN)
    @UseGuards(AuthGuard('oauth-bearer'), PermissionsGuard)
    @ApiResponse({
        status: 200,
        description: 'Get employee exit complete detail.',
    })
    @Get('/exit-detail/:id')
    public async getEmployeeExitDetail(
        @Param('id') id: number,
    ): Promise<any> {
        return this.employeeOffboardingService.getEmployeeExitDetail(id);
    }
}
