{"label": {"initiating_exit": "Initiating Exit", "my_profile": "My Profile", "logout": "Logout", "employee_offboarding": "Offboarding List", "tasks": "Initiated Exits", "filters": "Filters", "search_employee": "Search Employee", "last_physical_working_date": "Last Physical Working Date", "last_employment_date": "Last Employment Date", "department": "Department", "bsa": "Business Support Administrators", "exit_type": "Exit Type", "notice_period": "Notice Period", "additional_notes": "Notes", "action": "Action", "list_per_page": "List Per Page", "clear_filters": "Clear Filters", "searching": "Searching", "no_results_found": "No result found", "add_task": "Initiate Exit", "export_to_excel": "Export to excel", "view_details": "View Details", "initiate_offboarding": "Initiate Offboarding", "employee_name": "Employee Name", "employee_number": "Employee Number", "employee_email": "Employee Email", "job_title": "Job Title", "line_manager": "Line Manager", "leaving_group": "Leaving Group", "transferring_group": "Transferring Group", "notice_months": "Notice Period (In Months)", "notice_period_to_be_served": "Notice period to be served", "yes": "Yes", "no": "No", "last_physical_working_start_date": "Last physical working start date", "last_physical_working_end_date": "Last physical working end date", "last_employment_start_date": "Last employment start date", "last_employment_end_date": "Last employment end date", "status": "Status", "line_manager_email": "Line Manager <PERSON><PERSON>", "not_served": "Not Served", "my_tasks": "My Task", "emp_id": "Emp ID -", "click_to_download": "Click to Download", "reminder_confirmation_title": "Reminder Confirmation", "reminder_confirmation_desc": "Are you sure you want to send a reminder to", "send_remainder": "Send Reminder", "deductions": "Deductions (in AED)", "net_settlement": " Net Settlement (in AED)", "payments": "Payments (in AED)", "description_of_payment": "Description of Payment", "description_of_deduction": "Description of Deduction", "aed": "AED", "search_task": "Search Checklist", "notifications": "Notifications", "not_yet_started": "Not Yet Started", "all_completed": "All Completed", "completed": "Completed", "uploaded_files": "Uploaded Files", "through_your_machine": "through your machine", "drop_documnet_here_or_click": "Drop document here or click", "drop_or_select_document": "Drop or Select document", "browse": "browse", "documents_upload": "Documents Upload", "employee": "Employee", "submit_action": "Submit Action", "pending_with": "Pending with", "months": "Months", "assign_on": "Assigned On", "action_on": "Action On", "view_all": "View all", "submitting": "Submitting", "comments": "Comments", "approve": "Approve", "reject": "Reject"}, "placeholder": {"select": "Select", "filter": "Filter", "search": "Search", "select_bsa": "Select BSA", "select_business_entity": "Select Department", "department": "Department", "enter_comments": "Enter Comments"}, "btn_name": {"submit": "Submit", "next": "Next", "save": "Save", "cancel": "Cancel", "update": "Update", "close": "Close", "back": "Back", "edit": "Edit", "search": "Search", "reset": "Reset", "export": "Export", "send": "Send", "initiate": "Initiate", "complete": "Complete", "acknowledge": "Acknowledge"}, "error_messages": {"missing_required_fields": "Missing required fields. Check and try again", "permission_error": "Access Denied", "something_went_wrong": "An unexpected error occurred. Please try again later."}, "messages": {"entity_not_found": "No data available", "permission_error_location": "You don't have permission to access", "delete_confirmation": "Are you sure want to delete?", "are_you_sure_want_to_delete": "Are you sure want to delete?", "delete_success": "Delete success!", "warning": "Warning & Confirmation", "form_reset_title": "Confirmation", "do_you_wish_to_continue": "Do you wish to continue?", "no_files_upload": "No files uploaded", "field_is_required": "Field is required", "type_something_to_search": "Type something to search", "submitted_successfully": "Submitted successfully!", "no_heirarchy": "No heirarchy found", "bussiness_unit_error": "Something went wrong while fetching Business Units!", "searching_employee": "Searching employee", "fetching_bsa": "Fetching business support administrators", "no_checklist_available": "No checklist available", "offboarding_details_not_exist": "Offboarding detail doesn't exist.", "employee_details_not_exist": "Employee detail doesn't exist."}, "empty_state_messages": {"no_user_found": "No user found", "no_document_uploaded": "No document uploaded", "no_data_found": "No data found", "user_not_exist": "Employee details not available", "search_employee_to_intitate_offboarding": "Search employee to initiate offboarding", "no_information_submitted": "No Information Submitted", "checklist_dependent_on": "Waiting for IT Checklist Clearance - Returned Mobile Device to IT", "no_notifications_found": "No recent notifications", "no_pending_task": "No task has been assigned to you", "no_history_found": "No history found"}, "headings": {"filters": "Filters", "employee_details": "Employee Details", "initiate_offboarding": "Initiate Offboarding", "offboarding_details": "Offboarding Details", "approvers_details": "Approvers Details", "history_details": "History Details", "summary_of_payment_deduction": "Summary of Payment / Deduction", "employee_checklist": "Employee Checklist", "approver_action": "Approver Action", "acknowledgement_action": "Acknowledgement Action"}}