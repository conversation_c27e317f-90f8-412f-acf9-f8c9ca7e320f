import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { RequestContext } from 'src/shared/types';
import { BusinessEntityService } from '../services';
import { BusinessEntityResponseDto } from '../dtos';
import { BusinessEntityLevelResponseDto } from '../dtos/response/business-entity-level.dto';
import { BusinessEntityRoleResponseDto } from '../dtos/response/business-entity-role.dto';

@ApiTags('Business Entity APIs')
@ApiBearerAuth()
@UseGuards(AuthGuard('oauth-bearer'))
@Controller('business-entities')
export class BusinessEntityController {
	constructor(private readonly businessEntityService: BusinessEntityService) { }

	/**
	 * Get all business entities for a given permission for a user.
	 * @param request
	 * @param permission
	 * @param parentId
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Get all business entities for a given permission for a user',
		type: BusinessEntityResponseDto,
	})
	@ApiQuery({
		name: 'permission',
		type: String,
		description: 'User Permission.',
		required: false,
	})
	@ApiQuery({
		name: 'parentId',
		type: Number,
		description: 'Business Entity Parent Id (optional).',
		required: false,
	})
	@ApiQuery({
		name: 'lastLevel',
		type: String,
		description: 'Last Level To Get Till That Level Of Hierarchy (optional).',
		required: false,
	})
	@Get('')
	public getBusinessEntitiesForGivenPermissionForUser(
		@Req() request: RequestContext,
		@Query('permission') permission?: string,
		@Query('parentId') parentId?: number,
		@Query('lastLevel') lastLevel?: string,
	) {
		return this.businessEntityService.getBusinessEntitiesForGivenPermissionForUser(
			request.currentContext,
			permission,
			parentId,
			lastLevel
		);
	}

	/**
	 * Get all business entity levels.
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Get all business entities for a given permission for a user',
		type: [BusinessEntityLevelResponseDto],
	})
	@Get('/entity-levels')
	public getAllBusinessEntityLevels() {
		return this.businessEntityService.getAllBusinessEntityLevels();
	}

	/**
	 * Get all business entity roles.
	 * @params
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Get all business entities roles for given entity level',
		type: [BusinessEntityRoleResponseDto],
	})
	@Get('/entity-roles')
	public getAllBusinessEntityRoles(@Query('entityLevel') entityLevel: string) {
		return this.businessEntityService.getAllBusinessEntityRoles(entityLevel);
	}

	/**
	 * Get all business entity roles.
	 * @params
	 * @returns
	 */
	@ApiResponse({
		status: 200,
		description: 'Get BSA users for given entity id',
		// type: [BusinessEntityRoleResponseDto],
	})
	@Get('/:entityId/BSA')
	public getBSAOfSelectedEntity(@Query('entityId') entityId: number) {
		return this.businessEntityService.getBSAOfSelectedEntity(entityId);
	}
}
