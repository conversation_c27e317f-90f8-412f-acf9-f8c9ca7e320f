{"version": 3, "sources": ["../../@mui/material/Switch/Switch.js", "../../@mui/material/Switch/switchClasses.js"], "sourcesContent": ["'use client';\n\n// @inheritedComponent IconButton\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, darken, lighten } from '@mui/system/colorManipulator';\nimport capitalize from \"../utils/capitalize.js\";\nimport createSimplePaletteValueFilter from \"../utils/createSimplePaletteValueFilter.js\";\nimport SwitchBase from \"../internal/SwitchBase.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport switchClasses, { getSwitchUtilityClass } from \"./switchClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    edge,\n    size,\n    color,\n    checked,\n    disabled\n  } = ownerState;\n  const slots = {\n    root: ['root', edge && `edge${capitalize(edge)}`, `size${capitalize(size)}`],\n    switchBase: ['switchBase', `color${capitalize(color)}`, checked && 'checked', disabled && 'disabled'],\n    thumb: ['thumb'],\n    track: ['track'],\n    input: ['input']\n  };\n  const composedClasses = composeClasses(slots, getSwitchUtilityClass, classes);\n  return {\n    ...classes,\n    // forward the disabled and checked classes to the SwitchBase\n    ...composedClasses\n  };\n};\nconst SwitchRoot = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.edge && styles[`edge${capitalize(ownerState.edge)}`], styles[`size${capitalize(ownerState.size)}`]];\n  }\n})({\n  display: 'inline-flex',\n  width: 34 + 12 * 2,\n  height: 14 + 12 * 2,\n  overflow: 'hidden',\n  padding: 12,\n  boxSizing: 'border-box',\n  position: 'relative',\n  flexShrink: 0,\n  zIndex: 0,\n  // Reset the stacking context.\n  verticalAlign: 'middle',\n  // For correct alignment with the text.\n  '@media print': {\n    colorAdjust: 'exact'\n  },\n  variants: [{\n    props: {\n      edge: 'start'\n    },\n    style: {\n      marginLeft: -8\n    }\n  }, {\n    props: {\n      edge: 'end'\n    },\n    style: {\n      marginRight: -8\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      width: 40,\n      height: 24,\n      padding: 7,\n      [`& .${switchClasses.thumb}`]: {\n        width: 16,\n        height: 16\n      },\n      [`& .${switchClasses.switchBase}`]: {\n        padding: 4,\n        [`&.${switchClasses.checked}`]: {\n          transform: 'translateX(16px)'\n        }\n      }\n    }\n  }]\n});\nconst SwitchSwitchBase = styled(SwitchBase, {\n  name: 'MuiSwitch',\n  slot: 'SwitchBase',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.switchBase, {\n      [`& .${switchClasses.input}`]: styles.input\n    }, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'absolute',\n  top: 0,\n  left: 0,\n  zIndex: 1,\n  // Render above the focus ripple.\n  color: theme.vars ? theme.vars.palette.Switch.defaultColor : `${theme.palette.mode === 'light' ? theme.palette.common.white : theme.palette.grey[300]}`,\n  transition: theme.transitions.create(['left', 'transform'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  [`&.${switchClasses.checked}`]: {\n    transform: 'translateX(20px)'\n  },\n  [`&.${switchClasses.disabled}`]: {\n    color: theme.vars ? theme.vars.palette.Switch.defaultDisabledColor : `${theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[600]}`\n  },\n  [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n    opacity: 0.5\n  },\n  [`&.${switchClasses.disabled} + .${switchClasses.track}`]: {\n    opacity: theme.vars ? theme.vars.opacity.switchTrackDisabled : `${theme.palette.mode === 'light' ? 0.12 : 0.2}`\n  },\n  [`& .${switchClasses.input}`]: {\n    left: '-100%',\n    width: '300%'\n  }\n})), memoTheme(({\n  theme\n}) => ({\n  '&:hover': {\n    backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.activeChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette.action.active, theme.palette.action.hoverOpacity),\n    // Reset on touch devices, it doesn't add specificity\n    '@media (hover: none)': {\n      backgroundColor: 'transparent'\n    }\n  },\n  variants: [...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(['light'])) // check all the used fields in the style below\n  .map(([color]) => ({\n    props: {\n      color\n    },\n    style: {\n      [`&.${switchClasses.checked}`]: {\n        color: (theme.vars || theme).palette[color].main,\n        '&:hover': {\n          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.hoverOpacity})` : alpha(theme.palette[color].main, theme.palette.action.hoverOpacity),\n          '@media (hover: none)': {\n            backgroundColor: 'transparent'\n          }\n        },\n        [`&.${switchClasses.disabled}`]: {\n          color: theme.vars ? theme.vars.palette.Switch[`${color}DisabledColor`] : `${theme.palette.mode === 'light' ? lighten(theme.palette[color].main, 0.62) : darken(theme.palette[color].main, 0.55)}`\n        }\n      },\n      [`&.${switchClasses.checked} + .${switchClasses.track}`]: {\n        backgroundColor: (theme.vars || theme).palette[color].main\n      }\n    }\n  }))]\n})));\nconst SwitchTrack = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Track',\n  overridesResolver: (props, styles) => styles.track\n})(memoTheme(({\n  theme\n}) => ({\n  height: '100%',\n  width: '100%',\n  borderRadius: 14 / 2,\n  zIndex: -1,\n  transition: theme.transitions.create(['opacity', 'background-color'], {\n    duration: theme.transitions.duration.shortest\n  }),\n  backgroundColor: theme.vars ? theme.vars.palette.common.onBackground : `${theme.palette.mode === 'light' ? theme.palette.common.black : theme.palette.common.white}`,\n  opacity: theme.vars ? theme.vars.opacity.switchTrack : `${theme.palette.mode === 'light' ? 0.38 : 0.3}`\n})));\nconst SwitchThumb = styled('span', {\n  name: 'MuiSwitch',\n  slot: 'Thumb',\n  overridesResolver: (props, styles) => styles.thumb\n})(memoTheme(({\n  theme\n}) => ({\n  boxShadow: (theme.vars || theme).shadows[1],\n  backgroundColor: 'currentColor',\n  width: 20,\n  height: 20,\n  borderRadius: '50%'\n})));\nconst Switch = /*#__PURE__*/React.forwardRef(function Switch(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSwitch'\n  });\n  const {\n    className,\n    color = 'primary',\n    edge = false,\n    size = 'medium',\n    sx,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    color,\n    edge,\n    size\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: SwitchRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      sx\n    }\n  });\n  const [ThumbSlot, thumbSlotProps] = useSlot('thumb', {\n    className: classes.thumb,\n    elementType: SwitchThumb,\n    externalForwardedProps,\n    ownerState\n  });\n  const icon = /*#__PURE__*/_jsx(ThumbSlot, {\n    ...thumbSlotProps\n  });\n  const [TrackSlot, trackSlotProps] = useSlot('track', {\n    className: classes.track,\n    elementType: SwitchTrack,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [/*#__PURE__*/_jsx(SwitchSwitchBase, {\n      type: \"checkbox\",\n      icon: icon,\n      checkedIcon: icon,\n      ref: ref,\n      ownerState: ownerState,\n      ...other,\n      classes: {\n        ...classes,\n        root: classes.switchBase\n      },\n      slots: {\n        ...(slots.switchBase && {\n          root: slots.switchBase\n        }),\n        ...(slots.input && {\n          input: slots.input\n        })\n      },\n      slotProps: {\n        ...(slotProps.switchBase && {\n          root: typeof slotProps.switchBase === 'function' ? slotProps.switchBase(ownerState) : slotProps.switchBase\n        }),\n        ...(slotProps.input && {\n          input: typeof slotProps.input === 'function' ? slotProps.input(ownerState) : slotProps.input\n        })\n      }\n    }), /*#__PURE__*/_jsx(TrackSlot, {\n      ...trackSlotProps\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Switch.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the ripple effect is disabled.\n   * @default false\n   */\n  disableRipple: PropTypes.bool,\n  /**\n   * If given, uses a negative margin to counteract the padding on one\n   * side (this is often helpful for aligning the left or right\n   * side of the icon with content above or below, without ruining the border\n   * size and shape).\n   * @default false\n   */\n  edge: PropTypes.oneOf(['end', 'start', false]),\n  /**\n   * The icon to display when the component is unchecked.\n   */\n  icon: PropTypes.node,\n  /**\n   * The id of the `input` element.\n   */\n  id: PropTypes.string,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Attributes) applied to the `input` element.\n   * @deprecated Use `slotProps.input` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputProps: PropTypes.object,\n  /**\n   * Pass a ref to the `input` element.\n   * @deprecated Use `slotProps.input.ref` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  inputRef: refType,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * `small` is equivalent to the dense switch styling.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    switchBase: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    thumb: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    track: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    input: PropTypes.elementType,\n    root: PropTypes.elementType,\n    switchBase: PropTypes.elementType,\n    thumb: PropTypes.elementType,\n    track: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default Switch;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchClasses = generateUtilityClasses('MuiSwitch', ['root', 'edgeStart', 'edgeEnd', 'switchBase', 'colorPrimary', 'colorSecondary', 'sizeSmall', 'sizeMedium', 'checked', 'disabled', 'input', 'thumb', 'track']);\nexport default switchClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,YAAuB;AACvB,wBAAsB;;;ACFf,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,aAAa,WAAW,cAAc,gBAAgB,kBAAkB,aAAa,cAAc,WAAW,YAAY,SAAS,SAAS,OAAO,CAAC;AACvN,IAAO,wBAAQ;;;ADWf,yBAA2C;AAC3C,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,QAAQ,OAAO,mBAAW,IAAI,CAAC,IAAI,OAAO,mBAAW,IAAI,CAAC,EAAE;AAAA,IAC3E,YAAY,CAAC,cAAc,QAAQ,mBAAW,KAAK,CAAC,IAAI,WAAW,WAAW,YAAY,UAAU;AAAA,IACpG,OAAO,CAAC,OAAO;AAAA,IACf,OAAO,CAAC,OAAO;AAAA,IACf,OAAO,CAAC,OAAO;AAAA,EACjB;AACA,QAAM,kBAAkB,eAAe,OAAO,uBAAuB,OAAO;AAC5E,SAAO;AAAA,IACL,GAAG;AAAA;AAAA,IAEH,GAAG;AAAA,EACL;AACF;AACA,IAAM,aAAa,eAAO,QAAQ;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,QAAQ,OAAO,OAAO,mBAAW,WAAW,IAAI,CAAC,EAAE,GAAG,OAAO,OAAO,mBAAW,WAAW,IAAI,CAAC,EAAE,CAAC;AAAA,EACpI;AACF,CAAC,EAAE;AAAA,EACD,SAAS;AAAA,EACT,OAAO,KAAK,KAAK;AAAA,EACjB,QAAQ,KAAK,KAAK;AAAA,EAClB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA,EAER,eAAe;AAAA;AAAA,EAEf,gBAAgB;AAAA,IACd,aAAa;AAAA,EACf;AAAA,EACA,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,aAAa;AAAA,IACf;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,MAAM;AAAA,IACR;AAAA,IACA,OAAO;AAAA,MACL,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG;AAAA,QAC7B,OAAO;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,CAAC,MAAM,sBAAc,UAAU,EAAE,GAAG;AAAA,QAClC,SAAS;AAAA,QACT,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,UAC9B,WAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AAAA,EACF,CAAC;AACH,CAAC;AACD,IAAM,mBAAmB,eAAO,oBAAY;AAAA,EAC1C,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,YAAY;AAAA,MACzB,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG,OAAO;AAAA,IACxC,GAAG,WAAW,UAAU,aAAa,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,CAAC;AAAA,EACrF;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,eAAe,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,EACrJ,YAAY,MAAM,YAAY,OAAO,CAAC,QAAQ,WAAW,GAAG;AAAA,IAC1D,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,IAC9B,WAAW;AAAA,EACb;AAAA,EACA,CAAC,KAAK,sBAAc,QAAQ,EAAE,GAAG;AAAA,IAC/B,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,uBAAuB,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC5J;AAAA,EACA,CAAC,KAAK,sBAAc,OAAO,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,IACxD,SAAS;AAAA,EACX;AAAA,EACA,CAAC,KAAK,sBAAc,QAAQ,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,IACzD,SAAS,MAAM,OAAO,MAAM,KAAK,QAAQ,sBAAsB,GAAG,MAAM,QAAQ,SAAS,UAAU,OAAO,GAAG;AAAA,EAC/G;AAAA,EACA,CAAC,MAAM,sBAAc,KAAK,EAAE,GAAG;AAAA,IAC7B,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AACF,EAAE,GAAG,kBAAU,CAAC;AAAA,EACd;AACF,OAAO;AAAA,EACL,WAAW;AAAA,IACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,OAAO,aAAa,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,YAAY;AAAA;AAAA,IAEnM,wBAAwB;AAAA,MACtB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EACA,UAAU,CAAC,GAAG,OAAO,QAAQ,MAAM,OAAO,EAAE,OAAO,+BAA+B,CAAC,OAAO,CAAC,CAAC,EAC3F,IAAI,CAAC,CAAC,KAAK,OAAO;AAAA,IACjB,OAAO;AAAA,MACL;AAAA,IACF;AAAA,IACA,OAAO;AAAA,MACL,CAAC,KAAK,sBAAc,OAAO,EAAE,GAAG;AAAA,QAC9B,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,QAC5C,WAAW;AAAA,UACT,iBAAiB,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,KAAK,EAAE,WAAW,MAAM,MAAM,KAAK,QAAQ,OAAO,YAAY,MAAM,MAAM,MAAM,QAAQ,KAAK,EAAE,MAAM,MAAM,QAAQ,OAAO,YAAY;AAAA,UAC/L,wBAAwB;AAAA,YACtB,iBAAiB;AAAA,UACnB;AAAA,QACF;AAAA,QACA,CAAC,KAAK,sBAAc,QAAQ,EAAE,GAAG;AAAA,UAC/B,OAAO,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,GAAG,KAAK,eAAe,IAAI,GAAG,MAAM,QAAQ,SAAS,UAAU,QAAQ,MAAM,QAAQ,KAAK,EAAE,MAAM,IAAI,IAAI,OAAO,MAAM,QAAQ,KAAK,EAAE,MAAM,IAAI,CAAC;AAAA,QACjM;AAAA,MACF;AAAA,MACA,CAAC,KAAK,sBAAc,OAAO,OAAO,sBAAc,KAAK,EAAE,GAAG;AAAA,QACxD,kBAAkB,MAAM,QAAQ,OAAO,QAAQ,KAAK,EAAE;AAAA,MACxD;AAAA,IACF;AAAA,EACF,EAAE,CAAC;AACL,EAAE,CAAC;AACH,IAAM,cAAc,eAAO,QAAQ;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,cAAc,KAAK;AAAA,EACnB,QAAQ;AAAA,EACR,YAAY,MAAM,YAAY,OAAO,CAAC,WAAW,kBAAkB,GAAG;AAAA,IACpE,UAAU,MAAM,YAAY,SAAS;AAAA,EACvC,CAAC;AAAA,EACD,iBAAiB,MAAM,OAAO,MAAM,KAAK,QAAQ,OAAO,eAAe,GAAG,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,OAAO,QAAQ,MAAM,QAAQ,OAAO,KAAK;AAAA,EAClK,SAAS,MAAM,OAAO,MAAM,KAAK,QAAQ,cAAc,GAAG,MAAM,QAAQ,SAAS,UAAU,OAAO,GAAG;AACvG,EAAE,CAAC;AACH,IAAM,cAAc,eAAO,QAAQ;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,YAAY,MAAM,QAAQ,OAAO,QAAQ,CAAC;AAAA,EAC1C,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,cAAc;AAChB,EAAE,CAAC;AACH,IAAM,SAA4B,iBAAW,SAASA,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,OAAO;AAAA,IACP;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA;AAAA,EACF;AACA,QAAM,CAAC,UAAU,aAAa,IAAI,QAAQ,QAAQ;AAAA,IAChD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC,aAAa;AAAA,IACb;AAAA,IACA;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AACD,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,WAAoB,mBAAAC,KAAK,WAAW;AAAA,IACxC,GAAG;AAAA,EACL,CAAC;AACD,QAAM,CAAC,WAAW,cAAc,IAAI,QAAQ,SAAS;AAAA,IACnD,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb;AAAA,IACA;AAAA,EACF,CAAC;AACD,aAAoB,mBAAAC,MAAM,UAAU;AAAA,IAClC,GAAG;AAAA,IACH,UAAU,KAAc,mBAAAD,KAAK,kBAAkB;AAAA,MAC7C,MAAM;AAAA,MACN;AAAA,MACA,aAAa;AAAA,MACb;AAAA,MACA;AAAA,MACA,GAAG;AAAA,MACH,SAAS;AAAA,QACP,GAAG;AAAA,QACH,MAAM,QAAQ;AAAA,MAChB;AAAA,MACA,OAAO;AAAA,QACL,GAAI,MAAM,cAAc;AAAA,UACtB,MAAM,MAAM;AAAA,QACd;AAAA,QACA,GAAI,MAAM,SAAS;AAAA,UACjB,OAAO,MAAM;AAAA,QACf;AAAA,MACF;AAAA,MACA,WAAW;AAAA,QACT,GAAI,UAAU,cAAc;AAAA,UAC1B,MAAM,OAAO,UAAU,eAAe,aAAa,UAAU,WAAW,UAAU,IAAI,UAAU;AAAA,QAClG;AAAA,QACA,GAAI,UAAU,SAAS;AAAA,UACrB,OAAO,OAAO,UAAU,UAAU,aAAa,UAAU,MAAM,UAAU,IAAI,UAAU;AAAA,QACzF;AAAA,MACF;AAAA,IACF,CAAC,OAAgB,mBAAAA,KAAK,WAAW;AAAA,MAC/B,GAAG;AAAA,IACL,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhF,SAAS,kBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIvB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,aAAa,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhL,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI1B,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQzB,MAAM,kBAAAA,QAAU,MAAM,CAAC,OAAO,SAAS,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAI7C,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIhB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQV,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,MAAM,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxH,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,MAAM,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC5D,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAClE,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,IAC7D,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC/D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,IAChB,YAAY,kBAAAA,QAAU;AAAA,IACtB,OAAO,kBAAAA,QAAU;AAAA,IACjB,OAAO,kBAAAA,QAAU;AAAA,EACnB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,iBAAQ;", "names": ["Switch", "_jsx", "_jsxs", "PropTypes"]}