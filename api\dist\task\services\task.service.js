"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
const common_1 = require("@nestjs/common");
const class_transformer_1 = require("class-transformer");
const lodash_1 = require("lodash");
const clients_1 = require("../../shared/clients");
const task_api_client_1 = require("../../shared/clients/task-api.client");
const enums_1 = require("../../shared/enums");
const exceptions_1 = require("../../shared/exceptions");
const types_1 = require("../../shared/types");
const dtos_1 = require("../dtos");
const repositories_1 = require("../../employee-offboarding/repositories");
const helpers_1 = require("../../shared/helpers");
const config_service_1 = require("../../config/config.service");
const sequelize_1 = require("sequelize");
const services_1 = require("../../shared/services");
const services_2 = require("../../employee-offboarding/services");
let TaskService = class TaskService {
    constructor(taskApiClient, adminApiClient, mSGraphApiClient, offboardedEmployeeDetailRepository, checklistRepository, approverRepository, configService, databaseHelper, historyApiClient, sharedNotificationService, employeeOffboardingService) {
        this.taskApiClient = taskApiClient;
        this.adminApiClient = adminApiClient;
        this.mSGraphApiClient = mSGraphApiClient;
        this.offboardedEmployeeDetailRepository = offboardedEmployeeDetailRepository;
        this.checklistRepository = checklistRepository;
        this.approverRepository = approverRepository;
        this.configService = configService;
        this.databaseHelper = databaseHelper;
        this.historyApiClient = historyApiClient;
        this.sharedNotificationService = sharedNotificationService;
        this.employeeOffboardingService = employeeOffboardingService;
    }
    getUserDetails(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const { givenName: firstName, surname: lastName, userPrincipalName: upn, userType, mail: email, jobTitle, } = yield this.mSGraphApiClient.getUserDetails(userId);
            const loginId = userType == enums_1.AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
            return { firstName, lastName, loginId, email, jobTitle };
        });
    }
    getAllPendingUserTasks(currentContext, assignedTo = null) {
        return __awaiter(this, void 0, void 0, function* () {
            let { username } = currentContext.user;
            if (assignedTo) {
                const hasAdminPermission = yield this.adminApiClient.hasPermissionToUser(username, enums_1.PERMISSIONS.APPLICATION_ADMIN);
                if (!hasAdminPermission) {
                    throw new exceptions_1.HttpException(`You don't have permission to search task for other users.`, enums_1.HttpStatus.UNAUTHORIZED);
                }
                username = assignedTo;
            }
            const data = yield this.taskApiClient.getAllPendingUserTasks(username);
            return data.map(d => (0, class_transformer_1.instanceToPlain)(new dtos_1.TaskDetailResponseDto(d), { excludeExtraneousValues: true }));
        });
    }
    getTaskDetailById(id, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const taskDetail = yield this.validateAndGetTaskDetail(id, currentContext);
            const { entity_type, additional_info } = taskDetail;
            const { exitId, groupId } = additional_info;
            if (entity_type === types_1.TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK) {
                const offboardingEmployeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedTaskEmployeeDetailById(exitId, groupId);
                if (!offboardingEmployeeDetail) {
                    throw new exceptions_1.HttpException(`Offboarding detail doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                if (offboardingEmployeeDetail.status !== enums_1.OFFBOARDING_STATUS.IN_PROGRESS && offboardingEmployeeDetail.status !== enums_1.OFFBOARDING_STATUS.INITIATED) {
                    throw new exceptions_1.HttpException(`Offboarding is already ${offboardingEmployeeDetail.status.toLowerCase()}.`, enums_1.HttpStatus.BAD_REQUEST);
                }
                const { checklists } = offboardingEmployeeDetail, exitDetail = __rest(offboardingEmployeeDetail, ["checklists"]);
                const dependentCodeList = [];
                checklists.forEach(checklist => {
                    var _a;
                    if ((_a = checklist === null || checklist === void 0 ? void 0 : checklist.dependantChecklistCodes) === null || _a === void 0 ? void 0 : _a.length) {
                        dependentCodeList.push(...checklist.dependantChecklistCodes);
                    }
                });
                let dependantChecklist = [];
                if (dependentCodeList.length) {
                    dependantChecklist = yield this.checklistRepository.getExitChecklistByCodes(exitId, dependentCodeList);
                }
                return (0, helpers_1.singleObjectToInstance)(dtos_1.TaskActionDetailResponseDto, { exitDetail, checklists, taskDetail, dependantChecklist });
            }
            throw new exceptions_1.HttpException(`Invalid request.`, enums_1.HttpStatus.BAD_REQUEST);
        });
    }
    generateTaskEntityId(empId, approverId, groupId = 0) {
        const str = `${empId}-${approverId}-${groupId}`;
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            hash = (hash * 31 + str.charCodeAt(i)) | 0;
        }
        return Math.abs(hash);
    }
    submitChecklistAction(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { taskId, checklistId, actions } = payload;
            return this.databaseHelper.startTransaction(() => __awaiter(this, void 0, void 0, function* () {
                const { uiClient } = this.configService.getAppConfig();
                const taskDetail = yield this.validateAndGetTaskDetail(taskId, currentContext);
                const { exitId, groupId } = taskDetail.additional_info;
                const historyPayload = [];
                const offboardingEmployeeDetail = yield this.offboardedEmployeeDetailRepository.getOffboardedChecklistDetailById(exitId, groupId, checklistId);
                const checklistDetail = this.validateOffboardingAndChecklist(offboardingEmployeeDetail, checklistId);
                yield this.checklistRepository.updateChecklistActionById(checklistId, {
                    actionBy: currentContext.user.username,
                    actionOn: new Date(),
                    actionDetail: actions,
                }, currentContext);
                historyPayload.push({
                    created_by: currentContext.user.username,
                    entity_id: exitId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.SUBMITTED,
                    action_date: new Date(),
                    comments: `Checklist submitted by ${checklistDetail.groupDisplayName.toLowerCase()} - ${checklistDetail.checklistTitle.toLowerCase()}.`,
                    additional_info: actions
                });
                let closeChecklistGroupTask = false;
                const allGroupSpecificPendingChecklist = yield this.checklistRepository.pendingChecklistForExitGroup(exitId, groupId);
                const allExitPendingChecklist = yield this.checklistRepository.pendingChecklistForExit(exitId);
                const currentApprover = yield this.approverRepository.getCurrentPendingApproverForExit(exitId);
                if (allGroupSpecificPendingChecklist.length === 0) {
                    closeChecklistGroupTask = true;
                    if (allExitPendingChecklist.length === 0) {
                        if (!currentApprover) {
                            throw new exceptions_1.HttpException(`No pending approver found.`, enums_1.HttpStatus.BAD_REQUEST);
                        }
                        yield this.approverRepository.updateApproverActionByCondition({
                            id: currentApprover.id,
                        }, {
                            status: enums_1.APPROVER_STATUS.APPROVED,
                            actionBy: currentContext.user.username,
                            actionOn: new Date(),
                            comment: 'All checklist has been submitted.',
                        }, currentContext);
                        historyPayload.push({
                            created_by: 'All Departments',
                            entity_id: exitId,
                            entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                            action_performed: enums_1.HISTORY_ACTION_TYPE.APPROVED,
                            action_date: new Date(),
                            comments: 'All checklist has been submitted.',
                            additional_info: {}
                        });
                        const nextApprover = yield this.approverRepository.getNextApproverForExit(exitId, currentApprover.approvalSequence);
                        if (nextApprover) {
                            yield this.generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext);
                            historyPayload.push({
                                created_by: 'System',
                                entity_id: exitId,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                                action_date: new Date(),
                                comments: `Task has been assigned to ${nextApprover.title.toLowerCase()} for approval.`,
                                additional_info: {}
                            });
                        }
                        else {
                            yield this.markExitCompleted(offboardingEmployeeDetail, currentContext);
                            historyPayload.push({
                                created_by: 'System',
                                entity_id: exitId,
                                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                                action_performed: enums_1.HISTORY_ACTION_TYPE.APPROVED,
                                action_date: new Date(),
                                comments: `Exit offboarding completed.`,
                                additional_info: {}
                            });
                        }
                    }
                    else {
                        yield this.findAndAdjustDependentChecklist(allExitPendingChecklist, checklistDetail.code, offboardingEmployeeDetail, currentApprover, currentContext);
                        if (offboardingEmployeeDetail.status === enums_1.OFFBOARDING_STATUS.INITIATED) {
                            yield this.offboardedEmployeeDetailRepository.updateExitDetailById(exitId, { status: enums_1.OFFBOARDING_STATUS.IN_PROGRESS }, currentContext);
                        }
                    }
                }
                else {
                    yield this.findAndAdjustDependentChecklist(allExitPendingChecklist, checklistDetail.code, offboardingEmployeeDetail, currentApprover, currentContext);
                }
                if (closeChecklistGroupTask) {
                    yield this.taskApiClient.completeTask({
                        id: taskDetail.id,
                        outcome: 'approved',
                        created_by: currentContext.user.username,
                        comments: 'All checklist completed.',
                    });
                    yield this.sharedNotificationService.sendNotification(exitId, enums_1.NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK, { to: [offboardingEmployeeDetail.createdBy] }, enums_1.EMAIL_TEMPLATES.DEPARTMENT_CHECKLIST_CLEAR, {
                        departmentName: checklistDetail.groupDisplayName,
                        employeeName: offboardingEmployeeDetail.employeeDetail.fullName,
                        employeeId: offboardingEmployeeDetail.employeeDetail.personNumber,
                        offboardingDate: offboardingEmployeeDetail.createdOn.toDateString(),
                        viewDetailLink: `<a href="${(0, helpers_1.replaceUrlVariable)(`${uiClient.baseUrl}${types_1.TASK_REL_URL.CHECKLIST_DETAIL}`, { exitId })}">View Offboarding Detail</a>`,
                    });
                }
                if (historyPayload.length) {
                    yield this.historyApiClient.addBulkRequestHistory(historyPayload);
                }
                return {
                    message: 'Action submitted successfully.', data: {
                        isTaskCompleted: closeChecklistGroupTask
                    }
                };
            }));
        });
    }
    findAndAdjustDependentChecklist(allExitPendingChecklist, checklistCode, exitDetail, currentApprover, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const dependentChecklist = allExitPendingChecklist.filter((c) => { var _a; return (_a = c.dependantChecklistCodes) === null || _a === void 0 ? void 0 : _a.includes(checklistCode); });
            if (dependentChecklist.length) {
                let enableDependantChecklist = [];
                let removeDependantCodeFromList = [];
                dependentChecklist.forEach((checklist) => {
                    const dependantCodeList = checklist.dependantChecklistCodes;
                    if (dependantCodeList.length === 1) {
                        enableDependantChecklist.push(checklist.id);
                    }
                    if (dependantCodeList.length > 1) {
                        removeDependantCodeFromList.push(checklist);
                    }
                });
                if (enableDependantChecklist.length) {
                    yield this.checklistRepository.updateChecklistByCondition({ id: { [sequelize_1.Op.in]: enableDependantChecklist } }, {
                        dependantChecklistCodes: null,
                        assignedOn: new Date()
                    }, currentContext);
                    let emailPromises = [];
                    let { uiClient } = this.configService.getAppConfig();
                    for (let i = 0; i < enableDependantChecklist.length; i++) {
                        const checklistDetail = enableDependantChecklist[i];
                        const currentTaskList = yield this.taskApiClient.getAllTasks(this.generateTaskEntityId(exitDetail.id, currentApprover === null || currentApprover === void 0 ? void 0 : currentApprover.id, checklistDetail.uniqueGroupId), types_1.TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK);
                        const currentTaskDetail = currentTaskList.length ? currentTaskList[0] : null;
                        const usersPromise = this.employeeOffboardingService.getUsersEmailByRoleAndEntityId(checklistDetail.roleKey, exitDetail.entityId).then((users) => {
                            return this.sharedNotificationService.sendNotification(exitDetail.id, enums_1.NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK, { to: users }, enums_1.EMAIL_TEMPLATES.DEPENDANT_CHECKLIST_ASSIGNED, {
                                employeeName: exitDetail.employeeDetail.fullName,
                                employeeId: exitDetail.employeeDetail.personNumber,
                                departmentName: checklistDetail.groupDisplayName,
                                offboardingDate: exitDetail.createdOn.toDateString(),
                                assignedOn: new Date().toDateString(),
                                checklistName: checklistDetail.checklistTitle,
                                taskLink: currentTaskDetail ? `<a href="${(0, helpers_1.replaceUrlVariable)(`${uiClient.baseUrl}${types_1.TASK_REL_URL.CHECKLIST_TASK}`, { taskId: currentTaskDetail === null || currentTaskDetail === void 0 ? void 0 : currentTaskDetail.id })}">Submit Checklist</a>` : `<a href="${uiClient.baseUrl + types_1.TASK_REL_URL.MY_TASK}">My Task</a>`,
                            });
                        });
                        emailPromises.push(usersPromise);
                    }
                    if (emailPromises.length) {
                        yield Promise.all(emailPromises);
                    }
                }
                if (removeDependantCodeFromList.length) {
                    const removeDependantCodeUpdate = removeDependantCodeFromList.map((dependantChecklist) => {
                        const updatedCodes = (dependantChecklist.dependantChecklistCodes || []).filter((code) => code !== checklistCode);
                        return this.checklistRepository.updateChecklistByCondition({ id: dependantChecklist.id }, { dependantChecklistCodes: updatedCodes }, currentContext);
                    });
                    if (removeDependantCodeUpdate.length) {
                        yield Promise.all(removeDependantCodeUpdate);
                    }
                }
            }
        });
    }
    approverAction(payload, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { taskId, action, comment } = payload;
            const { taskDetail, currentApprover } = yield this.validateIfApproverTask(taskId, null, currentContext);
            const { additional_info } = taskDetail;
            const { exitId } = additional_info;
            const { id: approverId } = currentApprover;
            const historyPayload = [];
            const offboardingEmployeeDetail = yield this.offboardedEmployeeDetailRepository.getEmployeeDetailById(exitId);
            if (!offboardingEmployeeDetail) {
                throw new exceptions_1.HttpException(`Exit detail not found.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (offboardingEmployeeDetail.status !== enums_1.OFFBOARDING_STATUS.IN_PROGRESS) {
                throw new exceptions_1.HttpException(`Offboarding is not currently in the In-Progress state.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            yield this.taskApiClient.completeTask({
                id: taskDetail.id,
                outcome: action.toLowerCase(),
                created_by: currentContext.user.username,
                comments: comment,
            });
            yield this.approverRepository.updateApproverActionByCondition({
                id: approverId,
            }, {
                status: action,
                actionBy: currentContext.user.username,
                actionOn: new Date(),
                comment,
            }, currentContext);
            historyPayload.push({
                created_by: currentContext.user.username,
                entity_id: exitId,
                entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                action_performed: enums_1.HISTORY_ACTION_TYPE.ACTION,
                action_date: new Date(),
                comments: `${action.toLowerCase()} by ${currentApprover.title.toLowerCase()}.`,
                additional_info: {
                    comment
                }
            });
            if (action === enums_1.APPROVER_ACTION.APPROVED) {
                const nextApprover = yield this.approverRepository.getNextApproverForExit(exitId, currentApprover.approvalSequence);
                if (nextApprover) {
                    yield this.generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext);
                    historyPayload.push({
                        created_by: 'System',
                        entity_id: exitId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.CREATE,
                        action_date: new Date(),
                        comments: `Task has been assigned to ${nextApprover.title.toLowerCase()} for approval.`,
                        additional_info: {}
                    });
                }
                else {
                    yield this.markExitCompleted(offboardingEmployeeDetail, currentContext);
                    historyPayload.push({
                        created_by: 'System',
                        entity_id: exitId,
                        entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                        action_performed: enums_1.HISTORY_ACTION_TYPE.APPROVED,
                        action_date: new Date(),
                        comments: `Exit offboarding completed.`,
                        additional_info: {}
                    });
                }
            }
            else {
                yield this.offboardedEmployeeDetailRepository.updateExitDetailById(exitId, { status: enums_1.OFFBOARDING_STATUS.REJECTED, userStatus: 'Rejected' }, currentContext);
                historyPayload.push({
                    created_by: 'System',
                    entity_id: exitId,
                    entity_type: enums_1.HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                    action_performed: enums_1.HISTORY_ACTION_TYPE.REJECTED,
                    action_date: new Date(),
                    comments: `Exit offboarding rejected.`,
                    additional_info: {}
                });
            }
            if (historyPayload.length) {
                yield this.historyApiClient.addBulkRequestHistory(historyPayload);
            }
            return {
                message: `Exit successfully ${action.toLowerCase()}.`,
            };
        });
    }
    markExitCompleted(offboardingEmployeeDetail, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const { id: exitId } = offboardingEmployeeDetail;
            yield this.offboardedEmployeeDetailRepository.updateExitDetailById(exitId, {
                status: enums_1.OFFBOARDING_STATUS.APPROVED,
                userStatus: 'Completed'
            }, currentContext);
        });
    }
    generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.approverRepository.updateApproverActionByCondition({
                id: nextApprover.id,
            }, {
                status: enums_1.APPROVER_STATUS.IN_PROGRESS,
                assignedOn: new Date(),
            }, currentContext);
            const { uiClient } = this.configService.getAppConfig();
            const taskPayload = {
                title: `${offboardingEmployeeDetail.employeeDetail.fullName} - ${offboardingEmployeeDetail.employeeDetail.personNumber}`,
                assigned_to: nextApprover.role,
                entity_type: types_1.TASK_ENTITY_TYPE.EOP_APPROVER_TASK,
                entity_id: this.generateTaskEntityId(offboardingEmployeeDetail.id, nextApprover === null || nextApprover === void 0 ? void 0 : nextApprover.id),
                is_group_assignemnt: true,
                business_entity_id: offboardingEmployeeDetail.entityId,
                base_url: uiClient.baseUrl,
                rel_url: types_1.TASK_REL_URL.APPROVER_TASK,
                additional_info: Object.assign(Object.assign({}, offboardingEmployeeDetail.employeeDetail), { exitId: (0, lodash_1.toNumber)(offboardingEmployeeDetail.id), approverId: (0, lodash_1.toNumber)(nextApprover === null || nextApprover === void 0 ? void 0 : nextApprover.id), groupDisplayName: nextApprover.title, entityId: offboardingEmployeeDetail.entityId, entityCode: offboardingEmployeeDetail.entityCode, entityTitle: offboardingEmployeeDetail.entityTitle, lastPhysicalWorkingDate: offboardingEmployeeDetail.lastPhysicalWorkingDate, lastEmploymentDate: offboardingEmployeeDetail.lastEmploymentDate, isNoticePeriodServed: offboardingEmployeeDetail.isNoticePeriodServed, noticePeriod: offboardingEmployeeDetail.noticePeriod, exitType: offboardingEmployeeDetail.exitType })
            };
            yield this.taskApiClient.createTaskWithUseDelegation(taskPayload);
            yield this.offboardedEmployeeDetailRepository.updateExitDetailById(offboardingEmployeeDetail.id, { userStatus: `Waiting for ${nextApprover.title.toLowerCase()}.` }, currentContext);
        });
    }
    validateAndGetTaskDetail(taskId, currentContext) {
        return __awaiter(this, void 0, void 0, function* () {
            const taskDetail = yield this.taskApiClient.getTaskById(taskId);
            if (!taskDetail || (taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.task_status) !== 'Not Started') {
                throw new exceptions_1.HttpException(`Task doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if ((taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.entity_type) !== types_1.TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK) {
                throw new exceptions_1.HttpException(`Task details are restricted to the Department Checklist Clearance role.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            const isTaskBelongToUser = yield this.isTaskBelongToUser(taskDetail, currentContext.user.username);
            if (!isTaskBelongToUser) {
                throw new exceptions_1.HttpException(`You are not authorized to view this task.`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            return taskDetail;
        });
    }
    validateOffboardingAndChecklist(offboardingEmployeeDetail, checklistId) {
        if (!offboardingEmployeeDetail) {
            throw new exceptions_1.HttpException(`Checklist not found for this department task.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (offboardingEmployeeDetail.status !== enums_1.OFFBOARDING_STATUS.IN_PROGRESS && offboardingEmployeeDetail.status !== enums_1.OFFBOARDING_STATUS.INITIATED) {
            throw new exceptions_1.HttpException(`Offboarding is already ${offboardingEmployeeDetail.status.toLowerCase()}.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        const checklist = offboardingEmployeeDetail.checklists.find((c) => (0, lodash_1.toNumber)(c.id) === (0, lodash_1.toNumber)(checklistId));
        if (!checklist) {
            throw new exceptions_1.HttpException(`Checklist is not associated with this task.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        if (checklist.actionOn) {
            throw new exceptions_1.HttpException(`Checklist is already submitted.`, enums_1.HttpStatus.BAD_REQUEST);
        }
        return checklist;
    }
    isTaskBelongToUser(taskDetail, username) {
        return __awaiter(this, void 0, void 0, function* () {
            const { assigned_to, business_entity_id } = taskDetail;
            const isUserInGroup = yield this.adminApiClient.hasUserRole(username, assigned_to, business_entity_id);
            if (!isUserInGroup) {
                throw new exceptions_1.HttpException(`User doesn't has permission to this task.`, enums_1.HttpStatus.FORBIDDEN);
            }
            return true;
        });
    }
    validateIfApproverTask(taskId, exitId, currentContext) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function* () {
            const taskDetail = yield this.taskApiClient.getTaskById(taskId);
            if (!taskDetail || (taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.task_status) !== 'Not Started') {
                throw new exceptions_1.HttpException(`Task doesn't exist.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if ((taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.entity_type) !== types_1.TASK_ENTITY_TYPE.EOP_APPROVER_TASK) {
                throw new exceptions_1.HttpException(`Task details are restricted to the approvers only.`, enums_1.HttpStatus.BAD_REQUEST);
            }
            if (!exitId) {
                exitId = (0, lodash_1.toNumber)((_a = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _a === void 0 ? void 0 : _a.exitId);
            }
            else {
                if (!((_b = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _b === void 0 ? void 0 : _b.exitId) || ((_c = taskDetail === null || taskDetail === void 0 ? void 0 : taskDetail.additional_info) === null || _c === void 0 ? void 0 : _c.exitId) !== exitId) {
                    throw new exceptions_1.HttpException('Task does not belong to this exit.', enums_1.HttpStatus.BAD_REQUEST);
                }
            }
            const currentApprover = yield this.approverRepository.getCurrentPendingApproverForExit(exitId);
            if (!currentApprover) {
                throw new exceptions_1.HttpException('No pending approver found.', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (currentApprover.role !== taskDetail.assigned_to) {
                throw new exceptions_1.HttpException('Exit is not pending with this approver.', enums_1.HttpStatus.BAD_REQUEST);
            }
            if (currentApprover.approvalType === enums_1.APPROVAL_TYPE.CHECKLIST) {
                throw new exceptions_1.HttpException('Checklist task approval is restricted in this section.', enums_1.HttpStatus.BAD_REQUEST);
            }
            const isTaskBelongToUser = yield this.isTaskBelongToUser(taskDetail, currentContext.user.username);
            if (!isTaskBelongToUser) {
                throw new exceptions_1.HttpException(`You are not authorized to view this task.`, enums_1.HttpStatus.UNAUTHORIZED);
            }
            return { taskDetail, currentApprover };
        });
    }
};
TaskService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [task_api_client_1.TaskApiClient,
        clients_1.AdminApiClient,
        clients_1.MSGraphApiClient,
        repositories_1.OffboardedEmployeeDetailRepository,
        repositories_1.OffboardingEmployeeChecklistRepository,
        repositories_1.OffboardingEmployeeApproverRepository,
        config_service_1.ConfigService,
        helpers_1.DatabaseHelper,
        clients_1.HistoryApiClient,
        services_1.SharedNotificationService,
        services_2.EmployeeOffboardingService])
], TaskService);
exports.TaskService = TaskService;
//# sourceMappingURL=task.service.js.map