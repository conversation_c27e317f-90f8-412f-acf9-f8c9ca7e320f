import { Injectable } from "@nestjs/common";
import { BaseRepository } from "src/shared/repositories";
import { OffboardedEmployeeChecklist } from "../models";
import { CurrentContext } from "src/shared/types";

@Injectable()
export class OffboardingEmployeeChecklistRepository extends BaseRepository<OffboardedEmployeeChecklist> {
    constructor() {
        super(OffboardedEmployeeChecklist);
    }

    public async createExitChecklists(
        payload: any,
        currentContext: CurrentContext,
    ) {
        return this.insertMany(payload, currentContext);
    }
}