import { AdminApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { TaskApiClient } from 'src/shared/clients/task-api.client';
import { CurrentContext } from 'src/shared/types';
import { ApproverActionRequestDto, ChecklistActionRequestDto } from '../dtos';
import { OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from 'src/employee-offboarding/repositories';
import { DatabaseHelper } from 'src/shared/helpers';
import { MessageResponseDto } from 'src/shared/dtos';
import { ConfigService } from 'src/config/config.service';
import { SharedNotificationService } from 'src/shared/services';
import { EmployeeOffboardingService } from 'src/employee-offboarding/services';
export declare class TaskService {
    private readonly taskApiClient;
    private readonly adminApiClient;
    private readonly mSGraphApiClient;
    private readonly offboardedEmployeeDetailRepository;
    private readonly checklistRepository;
    private readonly approverRepository;
    private readonly configService;
    private readonly databaseHelper;
    private readonly historyApiClient;
    private readonly sharedNotificationService;
    private readonly employeeOffboardingService;
    constructor(taskApiClient: TaskApiClient, adminApiClient: AdminApiClient, mSGraphApiClient: MSGraphApiClient, offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository, checklistRepository: OffboardingEmployeeChecklistRepository, approverRepository: OffboardingEmployeeApproverRepository, configService: ConfigService, databaseHelper: DatabaseHelper, historyApiClient: HistoryApiClient, sharedNotificationService: SharedNotificationService, employeeOffboardingService: EmployeeOffboardingService);
    private getUserDetails;
    getAllPendingUserTasks(currentContext: CurrentContext, assignedTo?: string | null): Promise<Record<string, any>>;
    getTaskDetailById(id: number, currentContext: CurrentContext): Promise<Record<string, any>>;
    generateTaskEntityId(empId: number, approverId: number, groupId?: number): number;
    submitChecklistAction(payload: ChecklistActionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    findAndAdjustDependentChecklist(allExitPendingChecklist: any, checklistCode: any, exitDetail: any, currentApprover: any, currentContext: CurrentContext): Promise<void>;
    approverAction(payload: ApproverActionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto>;
    private markExitCompleted;
    private generateNewTaskForNextApprover;
    private validateAndGetTaskDetail;
    private validateOffboardingAndChecklist;
    isTaskBelongToUser(taskDetail: any, username: string): Promise<boolean>;
    validateIfApproverTask(taskId: any, exitId: any, currentContext: CurrentContext): Promise<{
        taskDetail: import("src/shared/types").TaskData;
        currentApprover: import("../../employee-offboarding/models").OffboardedEmployeeApprover;
    }>;
}
