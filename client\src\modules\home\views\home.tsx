import { Box, Button, Card, IconButton, MenuItem, Typography } from '@mui/material';
import { useEffect, useState } from 'react';

import StatCard from '@/components/custom-card/stats-card';
import CustomPopover, { usePopover } from '@/components/custom-popover';
import Iconify from '@/components/iconify';
import SvgColor from '@/components/svg-color/svg-color.tsx';
import { useResponsive } from '@/hooks/use-responsive.ts';
import { useTranslate } from '@/locales/use-locales.ts';
import { KeyValue } from '@/shared/models';
import { getIcon } from '@/shared/utils/get-icon.ts';
import { School, CheckCircle, EmojiEvents } from '@mui/icons-material';
import { Grid } from '@mui/system';
import TaskListing from '../components/listing/task-listing';
import AddTaskFormModal from '../components/modify/add-task';
import { dummyTaskListData } from '../config';

const HomePage = () => {
  const popover = usePopover();
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [showFilterChip, setShowFilterChip] = useState(false);
  const [taskFormOpen, setTaskFormOpen] = useState(false);

  const [filters, setFilters] = useState<Record<string, KeyValue[]>>(() => {
    try {
      const stored = localStorage.getItem('taskListFilters');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  });
  const [tempFilters, setTempFilters] = useState<Record<string, KeyValue[]>>(() => {
    try {
      const stored = localStorage.getItem('taskListFilters');
      return stored ? JSON.parse(stored) : {};
    } catch {
      return {};
    }
  });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const { t } = useTranslate();
  const isSmallScreen = useResponsive('down', 'sm');

  useEffect(() => {
    localStorage.setItem('taskListFilters', JSON.stringify(filters));
    const hasAnyFilters = Object.values(filters).some((arr: KeyValue[]) => arr?.length > 0);
    setShowFilterChip(hasAnyFilters);
  }, [filters]);

  const [sortBy, setSortBy] = useState('');
  const [sortDirection, setSortDirection] = useState<'desc' | 'asc'>('desc');

  // const { data: TaskListData, isFetching: _isTaskListFetching } = useQuery({
  //   queryKey: ['Task-list', page, rowsPerPage, filters, sortBy, sortDirection],
  //   queryFn: () =>
  //     getTaskList(
  //       {
  //         page: page + 1,
  //         limit: rowsPerPage,

  //         orderBy: sortBy,
  //         orderDirection: sortDirection,
  //       },
  //       apiFilters,
  //     ),
  //   onError: () => {
  //     enqueueSnackbar(t('error_messages.something_went_wrong'), { variant: 'error' });
  //   },
  //   keepPreviousData: false,
  // });

  const taskList = [];

  const resetFilters = () => {
    setTempFilters({});
    setFilters({});
    setPage(0);
    setShowFilterChip(false);
    setShowFilterDrawer(false);
  };

  const handleOpenTaskFormPopup = () => {
    setTaskFormOpen(true);
  };
  return (
    <>
      <Grid px={2} container spacing={3}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <StatCard
            title="Courses in progress"
            count={6}
            icon={<School sx={{ color: '#fff', fontSize: 28 }} />}
            iconBgColor="#f59e0b"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <StatCard
            title="Courses completed"
            count={3}
            icon={<CheckCircle sx={{ color: '#fff', fontSize: 28 }} />}
            iconBgColor="#10b981"
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <StatCard
            title="Certificates"
            count={2}
            icon={<EmojiEvents sx={{ color: '#fff', fontSize: 28 }} />}
            iconBgColor="#8b5cf6"
          />
        </Grid>
      </Grid>
      <Grid px={{ md: 2 }} container spacing={2} mt={2}>
        <Box overflow={'hidden'} flex={1}>
          <Card sx={{ border: 'solid 1px #E8D6D6', borderRadius: '7px', paddingTop: 2 }}>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2} px={2}>
              <Typography variant="mainTitle">{t('label.tasks')}</Typography>

              {isSmallScreen ? (
                <Box display="flex" alignItems="center">
                  <IconButton onClick={() => setShowFilterDrawer(true)}>
                    <SvgColor sx={{ height: 16 }} src={getIcon('Filter-B')} />
                  </IconButton>
                  {taskList.length > 0 && (
                    <IconButton>
                      <SvgColor sx={{ height: 16 }} src={getIcon('export')} />
                    </IconButton>
                  )}
                </Box>
              ) : (
                <Box display="flex" alignItems="center">
                  <Button
                    onClick={() => setShowFilterDrawer(true)}
                    variant="outlined"
                    size={isSmallScreen ? 'small' : 'medium'}
                    sx={{
                      bgcolor: 'white',
                      borderRadius: '24px',
                      textTransform: 'none',
                      whiteSpace: 'nowrap',
                      gap: 0.5,
                      mr: 1,
                    }}
                    startIcon={<SvgColor sx={{ height: 16 }} src={getIcon('Filter-B')} />}
                  >
                    {t('label.filters')}
                  </Button>
                  {taskList.length > 0 && (
                    <Button
                      type="button"
                      variant="contained"
                      startIcon={<SvgColor sx={{ height: 16 }} src={getIcon('export')} />}
                      size={isSmallScreen ? 'small' : 'medium'}
                      sx={{
                        borderRadius: '24px',
                        textTransform: 'none',
                        whiteSpace: 'nowrap',
                        gap: 0.5,
                      }}
                    >
                      {t('btn_name.export')}
                    </Button>
                  )}
                  <IconButton
                    color={popover.open ? 'inherit' : 'default'}
                    onClick={popover.onOpen}
                    sx={{
                      borderRadius: '50%',
                      width: 40,
                      height: 40,
                    }}
                  >
                    <Iconify icon="eva:more-vertical-fill" />
                  </IconButton>
                </Box>
              )}
            </Box>
            <CustomPopover open={popover.open} onClose={popover.onClose} arrow="top-right" sx={{ width: 140 }}>
              <MenuItem
                onClick={() => {
                  handleOpenTaskFormPopup();
                  popover.onClose();
                }}
              >
                {t('label.add_task')}
              </MenuItem>
            </CustomPopover>
            {taskFormOpen && <AddTaskFormModal onClose={() => setTaskFormOpen(false)} open={taskFormOpen} />}

            <TaskListing
              filters={filters}
              setFilters={setFilters}
              tempFilters={tempFilters}
              setTempFilters={setTempFilters}
              taskListData={dummyTaskListData}
              _isTaskListFetching={false}
              showFilterDrawer={showFilterDrawer}
              page={page}
              setPage={setPage}
              sortBy={sortBy}
              setSortBy={setSortBy}
              setSortDirection={setSortDirection}
              sortDirection={sortDirection}
              rowsPerPage={rowsPerPage}
              setRowsPerPage={setRowsPerPage}
              setShowFilterDrawer={() => setShowFilterDrawer(!showFilterDrawer)}
              showFilterChip={showFilterChip}
              setShowFilterChip={setShowFilterChip}
              resetFilters={resetFilters}
            />
          </Card>
        </Box>
      </Grid>
    </>
  );
};

export default HomePage;
