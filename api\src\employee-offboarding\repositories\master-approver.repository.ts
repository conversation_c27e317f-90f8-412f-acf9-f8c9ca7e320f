import { Injectable } from "@nestjs/common";
import { BaseRepository } from "src/shared/repositories";
import { MasterApprover } from "../models";
import { Op } from "sequelize";

@Injectable()
export class MasterApproverRepository extends BaseRepository<MasterApprover> {
    constructor() {
        super(MasterApprover);
    }

    public async getEntityMasterApprovers(entityId): Promise<MasterApprover[]> {
        return this.findAll({
            where: {
                entityId
            }
        });
    }
}