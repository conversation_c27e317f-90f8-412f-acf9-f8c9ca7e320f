import { Column, DataType, HasMany, Table } from 'sequelize-typescript';
import { EXIT_TYPE_ENUM, OFFBOARDING_STATUS } from 'src/shared/enums';
import { enumToArray } from 'src/shared/helpers';
import { BsaDetail } from 'src/shared/interfaces';
import { BaseModel } from 'src/shared/models';
import { EmployeeDetail } from 'src/shared/types';
import { OffboardedEmployeeApprover } from './offboarded-employee-approver.model';
import { OffboardedEmployeeChecklist } from './offboarded-employee-checklist.model';

@Table({
    tableName: 'data_offboarded_employee_details',
})
export class OffboardedEmployeeDetail extends BaseModel<OffboardedEmployeeDetail> {
    @Column({ field: 'employee_detail', type: DataType.JSONB, allowNull: false })
    public employeeDetail: EmployeeDetail;

    @Column({ field: 'entity_id', type: DataType.NUMBER, allowNull: false })
    public entityId: number;

    @Column({ field: 'entity_code', type: DataType.STRING, allowNull: false })
    public entityCode: string;

    @Column({ field: 'entity_title', type: DataType.STRING, allowNull: false })
    public entityTitle: string;

    @Column({ field: 'bsa_detail', type: DataType.JSONB, allowNull: false })
    public bsaDetail: BsaDetail[];

    @Column({ field: 'last_physical_working_date', type: DataType.DATE, allowNull: false })
    public lastPhysicalWorkingDate: Date;

    @Column({ field: 'last_employment_date', type: DataType.DATE, allowNull: false })
    public lastEmploymentDate: Date;

    @Column({ field: 'leaving_group', type: DataType.BOOLEAN, allowNull: true })
    public leavingGroup: boolean;

    @Column({ field: 'transferring_group', type: DataType.BOOLEAN, allowNull: true })
    public transferringGroup: boolean;

    @Column({ field: 'is_notice_period_served', type: DataType.BOOLEAN, allowNull: false })
    public isNoticePeriodServed: boolean;

    @Column({ field: 'notice_period', type: DataType.NUMBER, allowNull: true })
    public noticePeriod: number;

    @Column({
        field: 'exit_type',
        type: DataType.ENUM(...enumToArray(EXIT_TYPE_ENUM)),
        allowNull: false
    })
    public exitType: EXIT_TYPE_ENUM;

    @Column({
        field: 'status',
        type: DataType.ENUM(...enumToArray(OFFBOARDING_STATUS)),
        allowNull: false
    })
    public status: OFFBOARDING_STATUS;

    @Column({ field: 'user_status', type: DataType.STRING, allowNull: false })
    public userStatus: string;

    @Column({ field: 'note', type: DataType.TEXT, allowNull: true })
    public note: string;

    @HasMany(() => OffboardedEmployeeApprover)
    public approvers: OffboardedEmployeeApprover[];

    @HasMany(() => OffboardedEmployeeChecklist)
    public checklists: OffboardedEmployeeChecklist[];
}
