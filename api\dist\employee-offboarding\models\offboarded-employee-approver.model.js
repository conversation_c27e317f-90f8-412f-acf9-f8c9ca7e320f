"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffboardedEmployeeApprover = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const offboarded_employees_detail_model_1 = require("./offboarded-employees-detail.model");
const helpers_1 = require("../../shared/helpers");
const enums_1 = require("../../shared/enums");
let OffboardedEmployeeApprover = class OffboardedEmployeeApprover extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => offboarded_employees_detail_model_1.OffboardedEmployeeDetail),
    (0, sequelize_typescript_1.Column)({ field: 'offboarding_employee_detail_id', type: sequelize_typescript_1.DataType.BIGINT, allowNull: false }),
    __metadata("design:type", Number)
], OffboardedEmployeeApprover.prototype, "offboardingEmployeeDetailId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => offboarded_employees_detail_model_1.OffboardedEmployeeDetail),
    __metadata("design:type", offboarded_employees_detail_model_1.OffboardedEmployeeDetail)
], OffboardedEmployeeApprover.prototype, "offboardedEmployeeDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeApprover.prototype, "title", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'is_checklist_approver', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: false, defaultValue: false }),
    __metadata("design:type", Boolean)
], OffboardedEmployeeApprover.prototype, "isChecklistApprover", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'role', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeApprover.prototype, "role", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'approval_sequence', type: sequelize_typescript_1.DataType.INTEGER, allowNull: false }),
    __metadata("design:type", Number)
], OffboardedEmployeeApprover.prototype, "approvalSequence", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.APPROVER_STATUS)),
        allowNull: false
    }),
    __metadata("design:type", String)
], OffboardedEmployeeApprover.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'comment', type: sequelize_typescript_1.DataType.TEXT, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeApprover.prototype, "comment", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'assigned_on', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], OffboardedEmployeeApprover.prototype, "assignedOn", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'action_on', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], OffboardedEmployeeApprover.prototype, "actionOn", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'action_by', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeApprover.prototype, "actionBy", void 0);
OffboardedEmployeeApprover = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'data_offboarded_employee_approvers',
    })
], OffboardedEmployeeApprover);
exports.OffboardedEmployeeApprover = OffboardedEmployeeApprover;
//# sourceMappingURL=offboarded-employee-approver.model.js.map