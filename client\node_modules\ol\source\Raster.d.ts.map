{"version": 3, "file": "Raster.d.ts", "sourceRoot": "", "sources": ["Raster.js"], "names": [], "mappings": "AAkJA;;GAEG;AAEH;;;;;GAKG;AAEH;;;;;;;GAOG;AAEH;;;GAGG;AACH;IACE;;OAEG;IACH,oBAFW,gBAAgB,EAmE1B;IA9DC;;;OAGG;IACH,kBAAkC;IAwBlC;;;OAGG;IACH,iBAAuB;IAEvB;;;OAGG;IACH,eAAgB;IAEhB;;;OAGG;IACH,wBAA+C;IAC/C;;;OAGG;IACH,iBAAiB;IAEjB;;;OAGG;IACH,oBAAqB;IAErB;;;OAGG;IACH,aAAgB;IAGlB;;;;;;;;OAQG;IACH,gBAPW,KAAK,CAAC,SAAS,CAAC,uBAGhB,CAAS,IAAK,EAAL,KAAK,EAAE,IAAS,EAAT,SAAS,EAAE,IAAM,UAAG,IAAI,QAWlD;IAED;;;OAGG;IACH,cAFW,GAAG,QAOb;IAED;;OAEG;IACH,kBA+CC;IAED;;;;OAIG;IACH,wBAHW,MAAM,SACN,YAAY,QAWtB;IAED;;;OAGG;IACH,oBA2BC;CAYF;AA2CD;;;GAGG;AAEH;;GAEG;AAEH;;;;GAIG;AACH;IACE;;;;;OAKG;IACH,kBALW,MAAM,cACN,OAAO,WAAW,EAAE,UAAU,QAC9B,MAAO,KAAK,KAAQ,EA2B9B;IArBC;;;;OAIG;IACH,QAHU,OAAO,cAAc,EAAE,MAAM,CAGR;IAE/B;;;;OAIG;IACH,YAHU,MAAM,CAGyD;IAEzE;;;;;OAKG;IACH,UAAgB;CAEnB;;;;;;aAlca,KAAK,CAAC,WAAW,CAAC;;;;;;;;cAElB,OAAO;;;;WACP,MAAM;;;;YACN,MAAM;;;;;;;;0BA2HP,CAAS,IAAK,EAAL,KAAK,EAAE,IAAS,EAAT,SAAS,EAAE,IAAsB,EAAtB,CAAC,MAAO,KAAK,KAAQ,CAAC,KAAG,IAAI;;;;;;;;;YAMvD,KAAK,CAAC,SAAS,CAAC;;;;cAChB,WAAW;;;;;;aAKX,MAAM;;;;eACN,SAAS;;;;;;;;;;WAET,MAAM;;;;;;;;;;;;;;;;;;;;;wBA4OP,CAAS,IAAuC,EAAvC,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,IAAM,UACpE,CAAO,KAAK,CAAC,MAAM,CAAC,GAAC,SAAS,CAAC;;;;kCA0BnB,OAAO,GAAG,OAAO;qCAKjB,OAAO,YAAY,EAAE,qBAAqB,GAAC,kBAAkB,GAAC,iBAAiB;;;;;;aA4C9E,KAAK,CAAC,OAAO,aAAa,EAAE,OAAO,GAAC,OAAO,mBAAmB,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAsBzE,MAAM,IACN,OAAO,eAAe,EAAE,WAAW,CAAC,OAAO,eAAe,EAAE,UAAU,EAAE,OAAO,oBAAoB,EAAE,OAAO,EAAE,MAAM,CAAC,GACjI,OAAW,eAAe,EAAE,WAAW,CAAC,OAAO,oBAAoB,EAAE,KAAK,EAAE,OAAO,WAAW,EAAE,WAAW,EAAE,MAAM,CAAC,GACpH,OAAW,eAAe,EAAE,WAAW,CAAC,OAAO,YAAY,EAAE,qBAAqB,EAAE,OAAO,YAAY,EAAE,gBAAgB,EAAE,MAAM,CAAC,GAClI,OAAW,eAAe,EAAE,WAAW,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,MAAM,CAAC,GAC1F,OAAW,eAAe,EAAE,mBAAmB,CAAC,OAAO,eAAe,EAAE,UAAU,GAAC,OAAO,oBAAoB,EAAE,KAAK,GAC9G,sBAAsB,EAAE,MAAM,CAAC;uBAnfhB,kBAAkB;kBAIvB,oBAAoB;AAidtC;;;;;;;;;;;;;;;;;;;;;GAqBG;AAEH;;;;;;;;GAQG;AAEH;;;;;;;;GAQG;AACH;IACE;;OAEG;IACH,qBAFW,OAAO,EA4IjB;IArIC;;OAEG;IACH,IAFU,uBAAuB,CAAC,OAAO,WAAW,EAAE,SAAS,CAAC,CAEzD;IAEP;;OAEG;IACH,MAFU,uBAAuB,CAAC,OAAO,WAAW,EAAE,SAAS,CAAC,CAEvD;IAET;;OAEG;IACH,IAFU,uBAAuB,CAAC,IAAI,CAAC,CAEhC;IAEP;;;OAGG;IACH,mBAAsB;IAEtB;;;OAGG;IACH,uBACuE;IAEvE;;;OAGG;IACH,iBAAmE;IAEnE;;;OAGG;IACH,gBAA4C;IAO5C;;;OAGG;IACH,wBAAmD;IAEnD;;;OAGG;IACH,mBAEmC;IAEnC;;;;OAIG;IACH,6BAAyB;IAEzB;;;;OAIG;IACH,6BAAgC;IAEhC;;;;OAIG;IACH,0BAAsB;IAEtB;;;OAGG;IACH,oBAsBC;IA6BH;;;;;;OAMG;IACH,wBALW,SAAS,mBAkBnB;IAED;;;;;;;OAOG;IACH,0BA0BC;IAED;;;;OAIG;IACH,yBAWC;IAED;;;;;;;OAOG;IACH,0BAPW,OAAO,cAAc,EAAE,MAAM,cAC7B,MAAM,cACN,MAAM,cACN,OAAO,uBAAuB,EAAE,OAAO,GACtC,OAAO,mBAAmB,EAAE,OAAO,CAsC9C;IAED;;;OAGG;IACH,wBAwBC;IAED;;;;;;;OAOG;IACH,0BAyCC;IAED;;;;OAIG;IACH,qCAJW,OAAO,oBAAoB,EAAE,OAAO,GACnC,KAAK,CAAC,MAAM,CAAC,GAAC,IAAI,CAkB7B;CAWF;wBA72BuB,YAAY"}