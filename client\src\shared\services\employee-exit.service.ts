import { ReplaceUrlVariable } from '@/routes/paths';
import * as http from '@/shared/services';
import { API_PATHS } from '../constants';
import { EmployeeExitResponse } from '../models/employee-exit-response.model';

export const getEmployeeExitDetails = (exitId: string, taskId?: string): Promise<EmployeeExitResponse> => {
  const params = taskId ? { taskId } : undefined;
  return http.get<EmployeeExitResponse>(ReplaceUrlVariable(API_PATHS.EMPLOYEE.GET_EXIT_DETAIL, { exitId }), { params });
};

export const sendReminder = (payload: any): Promise<any> => {
  return http.post<any>(API_PATHS.EMPLOYEE.SEND_REMINDER, payload);
};
