import { BelongsTo, Column, DataType, ForeignKey, Table } from "sequelize-typescript";
import { BaseModel } from "src/shared/models";
import { OffboardedEmployeeDetail } from "./offboarded-employees-detail.model";
import { enumToArray } from "src/shared/helpers";
import { APPROVER_STATUS } from "src/shared/enums";

@Table({
    tableName: 'data_offboarded_employee_approvers',
})
export class OffboardedEmployeeApprover extends BaseModel<OffboardedEmployeeApprover> {
    @ForeignKey(() => OffboardedEmployeeDetail)
    @Column({ field: 'offboarding_employee_detail_id', type: DataType.BIGINT, allowNull: false })
    public offboardingEmployeeDetailId: number;

    @BelongsTo(() => OffboardedEmployeeDetail)
    public offboardedEmployeeDetail: OffboardedEmployeeDetail;

    @Column({ field: 'title', type: DataType.STRING, allowNull: false })
    public title: string;

    @Column({ field: 'is_checklist_approver', type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
    public isChecklistApprover: boolean;

    @Column({ field: 'role', type: DataType.STRING, allowNull: true })
    public role: string;

    @Column({ field: 'approval_sequence', type: DataType.INTEGER, allowNull: false })
    public approvalSequence: number;

    @Column({
        field: 'status',
        type: DataType.ENUM(...enumToArray(APPROVER_STATUS)),
        allowNull: false
    })
    public status: APPROVER_STATUS;

    @Column({ field: 'comment', type: DataType.TEXT, allowNull: true })
    public comment: string;

    @Column({ field: 'assigned_on', allowNull: true, type: 'TIMESTAMP' })
    public assignedOn?: Date | null;

    @Column({ field: 'action_on', allowNull: true, type: 'TIMESTAMP' })
    public actionOn?: Date | null;

    @Column({ field: 'action_by', type: DataType.STRING, allowNull: true })
    public actionBy: string;
}
