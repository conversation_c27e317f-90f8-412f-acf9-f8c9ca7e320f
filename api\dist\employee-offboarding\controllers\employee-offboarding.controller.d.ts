import { RequestContext } from 'src/shared/types';
import { EmployeeOffboardingService } from '../services';
import { MessageResponseDto } from 'src/shared/dtos';
import { FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto } from '../dtos';
export declare class EmployeeOffboardingController {
    private readonly employeeOffboardingService;
    constructor(employeeOffboardingService: EmployeeOffboardingService);
    initiateNewExit(request: RequestContext, newExitRequest: InitiateNewExitRequest): Promise<MessageResponseDto>;
    getEmployeeOffboardingList(page?: number, limit?: number, orderBy?: string, orderDirection?: string, filterDto?: FilterRequestDto): Promise<PaginatedEmployeeListResponseDto>;
    getEmployeeExitDetail(id: number): Promise<any>;
}
