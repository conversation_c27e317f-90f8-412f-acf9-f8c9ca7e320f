import { BaseModel } from "src/shared/models";
import { ALLOWED_ACTION_TYPE } from "src/shared/types";
import { OffboardedEmployeeDetail } from "./offboarded-employees-detail.model";
export declare class OffboardedEmployeeChecklist extends BaseModel<OffboardedEmployeeChecklist> {
    offboardingEmployeeDetailId: number;
    offboardedEmployeeDetail: OffboardedEmployeeDetail;
    checklistTitle: string;
    groupDisplayName: string;
    roleKey: string;
    roleType: string;
    allowedActions: ALLOWED_ACTION_TYPE[];
    dependantChecklistCodes: string[];
    code: string;
    sla: string;
    assignedOn?: Date | null;
    actionOn?: Date | null;
    actionBy: string;
    actionDetail: any;
    visibilityPermission: string[];
}
