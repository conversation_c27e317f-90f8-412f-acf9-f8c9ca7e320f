{"version": 3, "file": "Text.d.ts", "sourceRoot": "", "sources": ["Text.js"], "names": [], "mappings": ";;;;;;;4BAOa,OAAO,GAAG,MAAM;0BAQhB,MAAM,GAAG,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwCG;AAEH;;;;GAIG;AACH;IACE;;OAEG;IACH,sBAFW,OAAO,EAiJjB;IA5IC;;;OAGG;IACH,cAAyB;IAEzB;;;OAGG;IACH,kBAAiC;IAEjC;;;OAGG;IACH,wBAA6C;IAE7C;;;OAGG;IACH,qBAAuC;IAEvC;;;OAGG;IACH,eAA2B;IAE3B;;;OAGG;IACH,oBAA0E;IAE1E;;;OAGG;IACH,cAAyB;IAEzB;;;OAGG;IACH,mBAAmC;IAEnC;;;OAGG;IACH,iBAA+B;IAE/B;;;OAGG;IACH,gBAA6B;IAE7B;;;OAGG;IACH,sBAAyC;IAEzC;;;OAGG;IACH,cAG2C;IAE3C;;;OAGG;IACH,kBACiE;IAEjE;;;OAGG;IACH,mBAC+D;IAE/D;;;OAGG;IACH,kBAAmC;IAEnC;;;OAGG;IACH,gBAAmE;IAEnE;;;OAGG;IACH,iBAAmE;IAEnE;;;OAGG;IACH,iBAAmE;IAEnE;;;OAGG;IACH,wBAEQ;IAER;;;OAGG;IACH,0BAEQ;IAER;;;OAGG;IACH,iBAAsE;IAEtE;;;OAGG;IACH,uBAA2C;IAG7C;;;;OAIG;IACH,SAHY,IAAI,CAgCf;IAED;;;;OAIG;IACH,eAHY,OAAO,CAKlB;IAED;;;;OAIG;IACH,WAHY,MAAM,GAAC,SAAS,CAK3B;IAED;;;;OAIG;IACH,eAHY,MAAM,CAKjB;IAED;;;;OAIG;IACH,gBAHY,aAAa,CAKxB;IAED;;;;OAIG;IACH,aAHY,MAAM,GAAC,SAAS,CAK3B;IAED;;;;OAIG;IACH,cAHY,MAAM,CAKjB;IAED;;;;OAIG;IACH,cAHY,MAAM,CAKjB;IAED;;;;OAIG;IACH,WAHY,OAAO,WAAW,EAAE,OAAO,GAAC,IAAI,CAK3C;IAED;;;;OAIG;IACH,qBAHY,OAAO,GAAC,SAAS,CAK5B;IAED;;;;OAIG;IACH,kBAHY,OAAO,GAAC,SAAS,CAK5B;IAED;;;;OAIG;IACH,eAHY,MAAM,GAAC,SAAS,CAK3B;IAED;;;;OAIG;IACH,YAHY,MAAM,GAAC,OAAO,YAAY,EAAE,IAAI,GAAC,SAAS,CAKrD;IAED;;;OAGG;IACH,iBAFY,OAAO,YAAY,EAAE,IAAI,CAIpC;IAED;;;;OAIG;IACH,aAHY,OAAO,aAAa,EAAE,OAAO,GAAC,IAAI,CAK7C;IAED;;;;OAIG;IACH,WAHY,MAAM,GAAC,KAAK,CAAC,MAAM,CAAC,GAAC,SAAS,CAKzC;IAED;;;;OAIG;IACH,gBAHY,eAAe,GAAC,SAAS,CAKpC;IAED;;;;OAIG;IACH,cAHY,WAAW,GAAC,SAAS,CAKhC;IAED;;;;OAIG;IACH,mBAHY,kBAAkB,GAAC,SAAS,CAKvC;IAED;;;;OAIG;IACH,qBAHY,OAAO,WAAW,EAAE,OAAO,GAAC,IAAI,CAK3C;IAED;;;;OAIG;IACH,uBAHY,OAAO,aAAa,EAAE,OAAO,GAAC,IAAI,CAK7C;IAED;;;;OAIG;IACH,cAHY,KAAK,CAAC,MAAM,CAAC,GAAC,IAAI,CAK7B;IAED;;;;OAIG;IACH,oBAHY,OAAO,YAAY,EAAE,aAAa,CAK7C;IAED;;;;;OAKG;IACH,sBAHW,OAAO,QAKjB;IAED;;;;;OAKG;IACH,cAHW,MAAM,GAAC,SAAS,QAK1B;IAED;;;;;OAKG;IACH,sBAHW,MAAM,QAKhB;IAED;;;;;OAKG;IACH,oBAHW,MAAM,QAKhB;IAED;;;;;OAKG;IACH,oBAHW,MAAM,QAKhB;IAED;;;;;OAKG;IACH,wBAHW,aAAa,QAKvB;IAED;;;;OAIG;IACH,mBAHW,MAAM,GAAC,SAAS,QAK1B;IAED;;;;;OAKG;IACH,kCAHW,OAAO,QAKjB;IAED;;;;;OAKG;IACH,4BAHW,OAAO,QAKjB;IAED;;;;;OAKG;IACH,cAHW,OAAO,WAAW,EAAE,OAAO,GAAC,IAAI,QAK1C;IAED;;;;;OAKG;IACH,sBAHW,MAAM,GAAC,SAAS,QAK1B;IAED;;;;;OAKG;IACH,gBAHW,MAAM,GAAC,OAAO,YAAY,EAAE,IAAI,GAAC,SAAS,QAMpD;IAED;;;;;OAKG;IACH,kBAHW,OAAO,aAAa,EAAE,OAAO,GAAC,IAAI,QAK5C;IAED;;;;;OAKG;IACH,cAHW,MAAM,GAAC,KAAK,CAAC,MAAM,CAAC,GAAC,SAAS,QAKxC;IAED;;;;;OAKG;IACH,wBAHW,eAAe,GAAC,SAAS,QAKnC;IAED;;;;;OAKG;IACH,oBAHW,WAAW,GAAC,SAAS,QAK/B;IAED;;;;;OAKG;IACH,8BAHW,kBAAkB,GAAC,SAAS,QAKtC;IAED;;;;;OAKG;IACH,wBAHW,OAAO,WAAW,EAAE,OAAO,GAAC,IAAI,QAK1C;IAED;;;;;OAKG;IACH,4BAHW,OAAO,aAAa,EAAE,OAAO,GAAC,IAAI,QAK5C;IAED;;;;;OAKG;IACH,oBAHW,KAAK,CAAC,MAAM,CAAC,GAAC,IAAI,QAK5B;CACF;iBA1oBgB,WAAW"}