"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffboardingEmployeeModule = void 0;
const common_1 = require("@nestjs/common");
const controllers_1 = require("./controllers");
const services_1 = require("./services");
const offboarded_employees_detail_repository_1 = require("./repositories/offboarded-employees-detail.repository");
const clients_1 = require("../shared/clients");
const services_2 = require("../shared/services");
const helpers_1 = require("../shared/helpers");
const repositories_1 = require("./repositories");
const repositories = [
    offboarded_employees_detail_repository_1.OffboardedEmployeeDetailRepository,
    repositories_1.MasterRoleChecklistRepository,
    repositories_1.MasterApproverRepository,
    repositories_1.OffboardingEmployeeChecklistRepository,
    repositories_1.OffboardingEmployeeApproverRepository,
];
let OffboardingEmployeeModule = class OffboardingEmployeeModule {
};
OffboardingEmployeeModule = __decorate([
    (0, common_1.Module)({
        controllers: [controllers_1.EmployeeOffboardingController],
        providers: [
            ...repositories,
            services_1.EmployeeOffboardingService,
            clients_1.EmployeeDataApiClient,
            clients_1.AdminApiClient,
            services_2.SharedPermissionService,
            clients_1.HistoryApiClient,
            helpers_1.DatabaseHelper,
        ],
    })
], OffboardingEmployeeModule);
exports.OffboardingEmployeeModule = OffboardingEmployeeModule;
//# sourceMappingURL=employee-offboarding.module.js.map