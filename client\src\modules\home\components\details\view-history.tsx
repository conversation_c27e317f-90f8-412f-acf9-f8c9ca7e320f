import { useTranslate } from '@/locales/use-locales';
import { HISTORY_TYPE } from '@/shared/enum';
import { getHistoryList } from '@/shared/services/history.service';
import { fDateTime } from '@/shared/utils';
import { capitalizeSentence } from '@/shared/utils/change-case';
import {
  Timeline,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  TimelineItem,
  timelineItemClasses,
  TimelineSeparator,
} from '@mui/lab';
import { Button, Card, CircularProgress, Typography } from '@mui/material';
import { Box, Grid, Stack } from '@mui/system';
import { isEmpty } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { useQuery } from 'react-query';

const actionByKey = 'by';
const type = HISTORY_TYPE.EXIT_EMPLOYEE;

const HistoryContent = ({ exitId }: any) => {
  const { t } = useTranslate();
  const [showAll, setShowAll] = useState(false);

  const {
    data: historyList,
    isFetching: _isHistoryListFetching,
    isError,
  } = useQuery({
    queryKey: ['employee-exit-history', exitId],
    queryFn: () => getHistoryList({ type, id: exitId }),
    onError: (error: any) => {
      enqueueSnackbar(typeof error?.message === 'string' ? error.message : t('error_messages.something_went_wrong'), {
        variant: 'error',
      });
    },
    keepPreviousData: false,
  });

  const itemsToShow = showAll ? historyList : historyList?.slice(0, 3);

  return (
    <Card
      sx={{
        background: '#FFFFFF',
        boxShadow: '0px 3px 6px #00000029',
        borderRadius: '17px',
        border: '1px solid #E8D6D6',
        px: 2,
        py: 2,
      }}
    >
      <Grid container>
        {/* Heading */}
        <Grid size={{ xs: 12 }}>
          <Stack pl={1} direction="row" justifyContent="space-between">
            <Typography variant="mainTitle">{t('headings.history_details')}</Typography>
          </Stack>
        </Grid>

        {/* Loader */}
        {_isHistoryListFetching ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '40vh',
              width: '100%',
            }}
          >
            <CircularProgress />
          </Box>
        ) : isError || isEmpty(historyList) ? (
          // Empty/Error state
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '40vh',
              textAlign: 'center',
              color: 'text.secondary',
              flexDirection: 'column',
              gap: 1,
              width: '100%',
            }}
          >
            <Typography variant="body1">{t('empty_state_messages.no_history_found')}</Typography>
          </Box>
        ) : (
          // Timeline
          <>
            <Timeline
              sx={{
                mt: 0,
                pl: 1,
                [`& .${timelineItemClasses.root}:before`]: {
                  flex: 0,
                  padding: 0,
                },
              }}
            >
              {itemsToShow?.map((item: any, index: number) => {
                const { comment, actionDate, actionBy } = item;
                return (
                  <TimelineItem key={index}>
                    <TimelineSeparator>
                      <TimelineDot sx={{ mt: 1.8 }} />
                      {index !== itemsToShow?.length - 1 && <TimelineConnector />}
                    </TimelineSeparator>
                    <TimelineContent>
                      <Typography variant="body1">{capitalizeSentence(comment)}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {actionByKey} {actionBy?.toLowerCase()}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {fDateTime(actionDate)}
                      </Typography>
                    </TimelineContent>
                  </TimelineItem>
                );
              })}
            </Timeline>

            {!showAll && historyList && historyList?.length > 3 && (
              <Box sx={{ textAlign: 'center', width: '100%' }}>
                <Button
                  variant="text"
                  onClick={() => setShowAll(true)}
                  color="primary"
                  sx={{
                    textTransform: 'none',
                    fontSize: '0.9rem',

                    '&:hover': {
                      textDecoration: 'underline',
                      backgroundColor: 'transparent',
                    },
                  }}
                >
                  {t('label.view_all')}
                </Button>
              </Box>
            )}
          </>
        )}
      </Grid>
    </Card>
  );
};

export default HistoryContent;
