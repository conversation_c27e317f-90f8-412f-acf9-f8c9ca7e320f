export interface CreateTask {
    assigned_to: any;
    entity_type: string;
    entity_id: number;
    is_group_assignemnt: boolean;
    business_entity_id: number;
    title?: string;
    details?: string;
    due_date?: Date;
    base_url?: string;
    rel_url?: string;
    additional_info?: any;
    created_by?: string;
    use_delegation?: boolean;
    delegate_filter_param?: {
        request_type_id?: number;
    };
}
export interface CancelTask {
    id: number;
    created_by?: string;
    comments?: string;
    additional_info?: any;
}
export interface CancelAllTask {
    entity_id: number;
    entity_type: string;
    comments?: string;
    created_by?: string;
}
export interface CancelOverDueTask {
    entity_type: string;
    created_by?: string;
    comments?: string;
}
export interface CompleteTask {
    id: number;
    outcome: string;
    created_by?: string;
    comments?: string;
    additional_info?: any;
}
export interface CompleteAllTask {
    entity_id: number;
    entity_type: string;
    outcome: string;
    assigned_to?: string;
    is_group_assignemnt?: boolean;
    created_by?: string;
    comments?: string;
}
export interface TaskData {
    id: number;
    tennant_id?: string;
    application_id?: string;
    business_entity_id?: number;
    entity_id?: number;
    entity_type?: string;
    title?: string;
    details?: string;
    assigned_to?: string;
    is_group_assignment?: boolean;
    due_date?: Date;
    task_base_url?: string;
    task_rel_url?: string;
    task_status?: string;
    task_outcome?: string;
    task_completion_date?: Date;
    task_completion_comments?: string;
    additional_info?: any;
    created_by?: string;
    created_on?: Date;
    modified_by?: string;
    modified_on?: Date;
    completed_by?: string;
    original_owner?: string;
    delegated_from_task_id?: number;
    delegated_task_type?: 'representative' | null;
}
export interface AddTaskResponse {
    task_id: number;
    assigned_to: any;
    type: 'delegate' | 'main';
}
export declare enum TASK_ENTITY_TYPE {
    EOP_CHECKLIST_TASK = "EOP_CHECKLIST_TASK",
    EOP_APPROVER_TASK = "EOP_APPROVER_TASK"
}
export declare enum TASK_REL_URL {
    CHECKLIST_TASK = "/my-task/{taskId}",
    CHECKLIST_DETAIL = "/employee-detail/{exitId}",
    APPROVER_TASK = "/employee-detail/{exitId}?taskId={taskId}",
    MY_TASK = "/my-task"
}
