import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { OFFBOARDING_STATUS } from "src/shared/enums";


class EmployeeDetailResponseDto {
    @ApiProperty()
    @Expose()
    public fullName: string;

    @ApiProperty()
    @Expose()
    public personNumber: string;

    @ApiProperty()
    @Expose()
    public loginId: string;

    @ApiProperty()
    @Expose()
    public workEmail: string;

    @ApiProperty()
    @Expose()
    public jobPosition: string;

    @ApiProperty()
    @Expose()
    public lineManagerName: string;

    @ApiProperty()
    @Expose()
    public lineManagerPersonNumber: string;

    @ApiProperty()
    @Expose()
    public lineManagerWorkEmail: string;

    @ApiProperty()
    @Expose()
    public region: string;

    @ApiProperty()
    @Expose()
    public businessUnit: string;

    @ApiProperty()
    @Expose()
    public department: string;
}

class BSAUserResponseDto {
    @ApiProperty()
    @Expose()
    public userName: string;
}

export class EmployeeListResponseDto {
    @ApiProperty()
    @Expose()
    public id: number;

    @ApiProperty({ type: EmployeeDetailResponseDto })
    @Expose()
    @Type(() => EmployeeDetailResponseDto)
    public employeeDetail?: EmployeeDetailResponseDto[];

    @ApiProperty()
    @Expose()
    public entityId: number;

    @ApiProperty()
    @Expose()
    public entityTitle: string;

    @ApiProperty()
    @Expose()
    public entityCode: string;

    @ApiProperty({ type: BSAUserResponseDto, isArray: true })
    @Expose()
    @Type(() => BSAUserResponseDto)
    public bsaDetail?: BSAUserResponseDto[];

    @ApiProperty()
    @Expose()
    public lastPhysicalWorkingDate: Date;

    @ApiProperty()
    @Expose()
    public lastEmploymentDate: Date;

    @ApiProperty()
    @Expose()
    public isNoticePeriodServed: boolean;

    @ApiProperty()
    @Expose()
    public noticePeriod: number;

    @ApiProperty()
    @Expose()
    public leavingGroup: boolean;

    @ApiProperty()
    @Expose()
    public transferringGroup: boolean;

    @ApiProperty()
    @Expose()
    public exitType: string;

    @ApiProperty()
    @Expose()
    public note: string;

    @ApiProperty()
    @Expose()
    public status: OFFBOARDING_STATUS;

    @ApiProperty()
    @Expose()
    public userStatus: string;

    @ApiProperty()
    @Expose()
    public createdAt: Date;

    @ApiProperty()
    @Expose()
    public createdBy: Date;

    @ApiProperty()
    @Expose()
    public updatedAt: Date;

    @ApiProperty()
    @Expose()
    public updatedBy: Date;

    constructor(partial: Partial<EmployeeListResponseDto> = {}) {
        Object.assign(this, partial);
    }
}

export class PaginatedEmployeeListResponseDto {
    @ApiProperty()
    @Expose()
    public pageTotal: number;

    @ApiProperty()
    @Expose()
    public total: number;

    @ApiProperty({ isArray: true, type: EmployeeListResponseDto })
    @Expose()
    @Type(() => EmployeeListResponseDto)
    public records: EmployeeListResponseDto[];
}
