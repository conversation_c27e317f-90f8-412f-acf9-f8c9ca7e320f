import { Module } from '@nestjs/common';
import { EmployeeOffboardingController } from './controllers';
import { EmployeeOffboardingService } from './services';
import { OffboardedEmployeeDetailRepository } from './repositories/offboarded-employees-detail.repository';
import { AdminApiClient, EmployeeDataApiClient, HistoryApiClient } from 'src/shared/clients';
import { SharedPermissionService } from 'src/shared/services';
import { DatabaseHelper } from 'src/shared/helpers';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from './repositories';

const repositories = [
    OffboardedEmployeeDetailRepository,
    MasterRoleChecklistRepository,
    MasterApproverRepository,
    OffboardingEmployeeChecklistRepository,
    OffboardingEmployeeApproverRepository,
];

@Module({
    controllers: [EmployeeOffboardingController],
    providers: [
        ...repositories,
        EmployeeOffboardingService,
        EmployeeDataApiClient,
        AdminApiClient,
        SharedPermissionService,
        HistoryApiClient,
        DatabaseHelper,
    ],
})
export class OffboardingEmployeeModule { }
