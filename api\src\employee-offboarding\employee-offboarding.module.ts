import { Module } from '@nestjs/common';
import { EmployeeOffboardingController } from './controllers';
import { EmployeeOffboardingService } from './services';
import { OffboardedEmployeeDetailRepository } from './repositories/offboarded-employees-detail.repository';
import { AdminApiClient, AttachmentApiClient, EmployeeDataApiClient, HistoryApiClient, MSGraphApiClient, NotificationApiClient } from 'src/shared/clients';
import { SharedAttachmentService, SharedNotificationService, SharedPermissionService } from 'src/shared/services';
import { DatabaseHelper } from 'src/shared/helpers';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from './repositories';
import { TaskApiClient } from 'src/shared/clients/task-api.client';
import { TaskService } from 'src/task/services';

const repositories = [
    OffboardedEmployeeDetailRepository,
    MasterRoleChecklistRepository,
    MasterApproverRepository,
    OffboardingEmployeeChecklistRepository,
    OffboardingEmployeeApproverRepository,
];

@Module({
    controllers: [EmployeeOffboardingController],
    providers: [
        ...repositories,
        EmployeeOffboardingService,
        EmployeeDataApiClient,
        AdminApiClient,
        TaskService,
        SharedPermissionService,
        TaskApiClient,
        MSGraphApiClient,
        HistoryApiClient,
        SharedAttachmentService,
        SharedNotificationService,
        NotificationApiClient,
        AttachmentApiClient,
        DatabaseHelper
    ],
})
export class OffboardingEmployeeModule { }
