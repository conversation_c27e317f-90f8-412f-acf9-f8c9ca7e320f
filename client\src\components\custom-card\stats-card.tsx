import { Box, Card, Typography } from '@mui/material';
import React from 'react';

interface StatCardProps {
  title: string;
  count: number | string;
  icon: React.ReactNode;
  iconBgColor: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, count, icon, iconBgColor }) => {
  return (
    <Card
      sx={{
        p: 3,
        borderRadius: 3,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
        backgroundColor: '#fff',
        textAlign: 'center',
        minHeight: 140,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.15)',
        }
      }}
    >
      <Box
        sx={{
          width: 56,
          height: 56,
          borderRadius: '50%',
          backgroundColor: iconBgColor,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          mb: 2,
          flexShrink: 0
        }}
      >
        {icon}
      </Box>

      <Typography
        variant="h3"
        sx={{
          fontWeight: 'bold',
          color: '#1f2937',
          mb: 1,
          fontSize: '2rem'
        }}
      >
        {count}
      </Typography>

      <Typography
        variant="body2"
        sx={{
          color: '#6b7280',
          fontSize: '0.875rem',
          fontWeight: 500
        }}
      >
        {title}
      </Typography>
    </Card>
  );
};

export default StatCard;
