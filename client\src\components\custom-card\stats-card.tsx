import { Box, Card, Typography } from '@mui/material';
import React from 'react';
import { School, CheckCircle, EmojiEvents } from '@mui/icons-material';

interface StatItem {
  title: string;
  count: number | string;
  icon: React.ReactNode;
  iconBgColor: string;
}

const StatsCard: React.FC = () => {
  const statsData: StatItem[] = [
    {
      title: 'Courses in progress',
      count: 6,
      icon: <School sx={{ color: '#fff', fontSize: 24 }} />,
      iconBgColor: '#f59e0b', // amber color
    },
    {
      title: 'Courses completed',
      count: 3,
      icon: <CheckCircle sx={{ color: '#fff', fontSize: 24 }} />,
      iconBgColor: '#10b981', // green color
    },
    {
      title: 'Certificates',
      count: 2,
      icon: <EmojiEvents sx={{ color: '#fff', fontSize: 24 }} />,
      iconBgColor: '#8b5cf6', // purple color
    },
  ];

  return (
    <Card
      sx={{
        p: 3,
        borderRadius: 2,
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
        backgroundColor: '#fff'
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 4
        }}
      >
        {statsData.map((stat, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              flex: 1
            }}
          >
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                backgroundColor: stat.iconBgColor,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0
              }}
            >
              {stat.icon}
            </Box>
            <Box>
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 'bold',
                  color: '#1f2937',
                  mb: 0.5
                }}
              >
                {stat.count}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  color: '#6b7280',
                  fontSize: '0.875rem'
                }}
              >
                {stat.title}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Card>
  );
};

export default StatsCard;
