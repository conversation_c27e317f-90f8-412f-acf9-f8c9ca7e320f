{"version": 3, "sources": ["../../@mui/material/Skeleton/Skeleton.js", "../../@mui/material/Skeleton/skeletonClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha, unstable_getUnit as getUnit, unstable_toUnitless as toUnitless } from \"../styles/index.js\";\nimport { keyframes, css, styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getSkeletonUtilityClass } from \"./skeletonClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    animation,\n    hasChildren,\n    width,\n    height\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, animation, hasChildren && 'withChildren', hasChildren && !width && 'fitContent', hasChildren && !height && 'heightAuto']\n  };\n  return composeClasses(slots, getSkeletonUtilityClass, classes);\n};\nconst pulseKeyframe = keyframes`\n  0% {\n    opacity: 1;\n  }\n\n  50% {\n    opacity: 0.4;\n  }\n\n  100% {\n    opacity: 1;\n  }\n`;\nconst waveKeyframe = keyframes`\n  0% {\n    transform: translateX(-100%);\n  }\n\n  50% {\n    /* +0.5s of delay between each loop */\n    transform: translateX(100%);\n  }\n\n  100% {\n    transform: translateX(100%);\n  }\n`;\n\n// This implementation is for supporting both Styled-components v4+ and Pigment CSS.\n// A global animation has to be created here for Styled-components v4+ (https://github.com/styled-components/styled-components/blob/main/packages/styled-components/src/utils/errors.md#12).\n// which can be done by checking typeof indeterminate1Keyframe !== 'string' (at runtime, Pigment CSS transform keyframes`` to a string).\nconst pulseAnimation = typeof pulseKeyframe !== 'string' ? css`\n        animation: ${pulseKeyframe} 2s ease-in-out 0.5s infinite;\n      ` : null;\nconst waveAnimation = typeof waveKeyframe !== 'string' ? css`\n        &::after {\n          animation: ${waveKeyframe} 2s linear 0.5s infinite;\n        }\n      ` : null;\nconst SkeletonRoot = styled('span', {\n  name: 'MuiSkeleton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.animation !== false && styles[ownerState.animation], ownerState.hasChildren && styles.withChildren, ownerState.hasChildren && !ownerState.width && styles.fitContent, ownerState.hasChildren && !ownerState.height && styles.heightAuto];\n  }\n})(memoTheme(({\n  theme\n}) => {\n  const radiusUnit = getUnit(theme.shape.borderRadius) || 'px';\n  const radiusValue = toUnitless(theme.shape.borderRadius);\n  return {\n    display: 'block',\n    // Create a \"on paper\" color with sufficient contrast retaining the color\n    backgroundColor: theme.vars ? theme.vars.palette.Skeleton.bg : alpha(theme.palette.text.primary, theme.palette.mode === 'light' ? 0.11 : 0.13),\n    height: '1.2em',\n    variants: [{\n      props: {\n        variant: 'text'\n      },\n      style: {\n        marginTop: 0,\n        marginBottom: 0,\n        height: 'auto',\n        transformOrigin: '0 55%',\n        transform: 'scale(1, 0.60)',\n        borderRadius: `${radiusValue}${radiusUnit}/${Math.round(radiusValue / 0.6 * 10) / 10}${radiusUnit}`,\n        '&:empty:before': {\n          content: '\"\\\\00a0\"'\n        }\n      }\n    }, {\n      props: {\n        variant: 'circular'\n      },\n      style: {\n        borderRadius: '50%'\n      }\n    }, {\n      props: {\n        variant: 'rounded'\n      },\n      style: {\n        borderRadius: (theme.vars || theme).shape.borderRadius\n      }\n    }, {\n      props: ({\n        ownerState\n      }) => ownerState.hasChildren,\n      style: {\n        '& > *': {\n          visibility: 'hidden'\n        }\n      }\n    }, {\n      props: ({\n        ownerState\n      }) => ownerState.hasChildren && !ownerState.width,\n      style: {\n        maxWidth: 'fit-content'\n      }\n    }, {\n      props: ({\n        ownerState\n      }) => ownerState.hasChildren && !ownerState.height,\n      style: {\n        height: 'auto'\n      }\n    }, {\n      props: {\n        animation: 'pulse'\n      },\n      style: pulseAnimation || {\n        animation: `${pulseKeyframe} 2s ease-in-out 0.5s infinite`\n      }\n    }, {\n      props: {\n        animation: 'wave'\n      },\n      style: {\n        position: 'relative',\n        overflow: 'hidden',\n        /* Fix bug in Safari https://bugs.webkit.org/show_bug.cgi?id=68196 */\n        WebkitMaskImage: '-webkit-radial-gradient(white, black)',\n        '&::after': {\n          background: `linear-gradient(\n                90deg,\n                transparent,\n                ${(theme.vars || theme).palette.action.hover},\n                transparent\n              )`,\n          content: '\"\"',\n          position: 'absolute',\n          transform: 'translateX(-100%)' /* Avoid flash during server-side hydration */,\n          bottom: 0,\n          left: 0,\n          right: 0,\n          top: 0\n        }\n      }\n    }, {\n      props: {\n        animation: 'wave'\n      },\n      style: waveAnimation || {\n        '&::after': {\n          animation: `${waveKeyframe} 2s linear 0.5s infinite`\n        }\n      }\n    }]\n  };\n}));\nconst Skeleton = /*#__PURE__*/React.forwardRef(function Skeleton(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSkeleton'\n  });\n  const {\n    animation = 'pulse',\n    className,\n    component = 'span',\n    height,\n    style,\n    variant = 'text',\n    width,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    animation,\n    component,\n    variant,\n    hasChildren: Boolean(other.children)\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(SkeletonRoot, {\n    as: component,\n    ref: ref,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ...other,\n    style: {\n      width,\n      height,\n      ...style\n    }\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Skeleton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The animation.\n   * If `false` the animation effect is disabled.\n   * @default 'pulse'\n   */\n  animation: PropTypes.oneOf(['pulse', 'wave', false]),\n  /**\n   * Optional children to infer width and height from.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Height of the skeleton.\n   * Useful when you don't want to adapt the skeleton to a text element but for instance a card.\n   */\n  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The type of content that will be rendered.\n   * @default 'text'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rectangular', 'rounded', 'text']), PropTypes.string]),\n  /**\n   * Width of the skeleton.\n   * Useful when the skeleton is inside an inline element with no width of its own.\n   */\n  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string])\n} : void 0;\nexport default Skeleton;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'text', 'rectangular', 'rounded', 'circular', 'pulse', 'wave', 'withChildren', 'fitContent', 'heightAuto']);\nexport default skeletonClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,YAAuB;AAEvB,wBAAsB;;;ACFf,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACA,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,QAAQ,eAAe,WAAW,YAAY,SAAS,QAAQ,gBAAgB,cAAc,YAAY,CAAC;AACjL,IAAO,0BAAQ;;;ADKf,yBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,WAAW,eAAe,gBAAgB,eAAe,CAAC,SAAS,cAAc,eAAe,CAAC,UAAU,YAAY;AAAA,EACjJ;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAatB,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkBrB,IAAM,iBAAiB,OAAO,kBAAkB,WAAW;AAAA,qBACtC,aAAa;AAAA,UACxB;AACV,IAAM,gBAAgB,OAAO,iBAAiB,WAAW;AAAA;AAAA,uBAElC,YAAY;AAAA;AAAA,UAEzB;AACV,IAAM,eAAe,eAAO,QAAQ;AAAA,EAClC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,WAAW,cAAc,SAAS,OAAO,WAAW,SAAS,GAAG,WAAW,eAAe,OAAO,cAAc,WAAW,eAAe,CAAC,WAAW,SAAS,OAAO,YAAY,WAAW,eAAe,CAAC,WAAW,UAAU,OAAO,UAAU;AAAA,EACrS;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,MAAM;AACJ,QAAM,aAAa,QAAQ,MAAM,MAAM,YAAY,KAAK;AACxD,QAAM,cAAc,WAAW,MAAM,MAAM,YAAY;AACvD,SAAO;AAAA,IACL,SAAS;AAAA;AAAA,IAET,iBAAiB,MAAM,OAAO,MAAM,KAAK,QAAQ,SAAS,KAAK,MAAM,MAAM,QAAQ,KAAK,SAAS,MAAM,QAAQ,SAAS,UAAU,OAAO,IAAI;AAAA,IAC7I,QAAQ;AAAA,IACR,UAAU,CAAC;AAAA,MACT,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,WAAW;AAAA,QACX,cAAc;AAAA,QACd,QAAQ;AAAA,QACR,iBAAiB;AAAA,QACjB,WAAW;AAAA,QACX,cAAc,GAAG,WAAW,GAAG,UAAU,IAAI,KAAK,MAAM,cAAc,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU;AAAA,QACjG,kBAAkB;AAAA,UAChB,SAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,cAAc;AAAA,MAChB;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,SAAS;AAAA,MACX;AAAA,MACA,OAAO;AAAA,QACL,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,MAC5C;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW;AAAA,MACjB,OAAO;AAAA,QACL,SAAS;AAAA,UACP,YAAY;AAAA,QACd;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW,eAAe,CAAC,WAAW;AAAA,MAC5C,OAAO;AAAA,QACL,UAAU;AAAA,MACZ;AAAA,IACF,GAAG;AAAA,MACD,OAAO,CAAC;AAAA,QACN;AAAA,MACF,MAAM,WAAW,eAAe,CAAC,WAAW;AAAA,MAC5C,OAAO;AAAA,QACL,QAAQ;AAAA,MACV;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO,kBAAkB;AAAA,QACvB,WAAW,GAAG,aAAa;AAAA,MAC7B;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA;AAAA,QAEV,iBAAiB;AAAA,QACjB,YAAY;AAAA,UACV,YAAY;AAAA;AAAA;AAAA,mBAGH,MAAM,QAAQ,OAAO,QAAQ,OAAO,KAAK;AAAA;AAAA;AAAA,UAGlD,SAAS;AAAA,UACT,UAAU;AAAA,UACV,WAAW;AAAA,UACX,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF,GAAG;AAAA,MACD,OAAO;AAAA,QACL,WAAW;AAAA,MACb;AAAA,MACA,OAAO,iBAAiB;AAAA,QACtB,YAAY;AAAA,UACV,WAAW,GAAG,YAAY;AAAA,QAC5B;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF,CAAC,CAAC;AACF,IAAM,WAA8B,iBAAW,SAASA,UAAS,SAAS,KAAK;AAC7E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ,YAAY;AAAA,IACZ;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,GAAG;AAAA,EACL,IAAI;AACJ,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,QAAQ,MAAM,QAAQ;AAAA,EACrC;AACA,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,cAAc;AAAA,IACrC,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH,OAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA,GAAG;AAAA,IACL;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,SAAS,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUlF,WAAW,kBAAAC,QAAU,MAAM,CAAC,SAAS,QAAQ,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAInD,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIhE,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,eAAe,WAAW,MAAM,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC;AACjE,IAAI;AACJ,IAAO,mBAAQ;", "names": ["Skeleton", "_jsx", "PropTypes"]}