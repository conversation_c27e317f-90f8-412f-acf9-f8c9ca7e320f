"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffboardedEmployeeChecklist = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const models_1 = require("../../shared/models");
const offboarded_employees_detail_model_1 = require("./offboarded-employees-detail.model");
let OffboardedEmployeeChecklist = class OffboardedEmployeeChecklist extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.ForeignKey)(() => offboarded_employees_detail_model_1.OffboardedEmployeeDetail),
    (0, sequelize_typescript_1.Column)({ field: 'offboarding_employee_detail_id', type: sequelize_typescript_1.DataType.BIGINT, allowNull: false }),
    __metadata("design:type", Number)
], OffboardedEmployeeChecklist.prototype, "offboardingEmployeeDetailId", void 0);
__decorate([
    (0, sequelize_typescript_1.BelongsTo)(() => offboarded_employees_detail_model_1.OffboardedEmployeeDetail),
    __metadata("design:type", offboarded_employees_detail_model_1.OffboardedEmployeeDetail)
], OffboardedEmployeeChecklist.prototype, "offboardedEmployeeDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'checklist_title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "checklistTitle", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'group_display_name', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "groupDisplayName", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'role_key', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "roleKey", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'role_type', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "roleType", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'allowed_actions', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Array)
], OffboardedEmployeeChecklist.prototype, "allowedActions", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'dependant_checklist_codes', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], OffboardedEmployeeChecklist.prototype, "dependantChecklistCodes", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "code", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'sla', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "sla", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'assigned_on', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], OffboardedEmployeeChecklist.prototype, "assignedOn", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'action_on', allowNull: true, type: 'TIMESTAMP' }),
    __metadata("design:type", Date)
], OffboardedEmployeeChecklist.prototype, "actionOn", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'action_by', type: sequelize_typescript_1.DataType.STRING, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeChecklist.prototype, "actionBy", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'action_detail', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Object)
], OffboardedEmployeeChecklist.prototype, "actionDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'visibility_permission', type: sequelize_typescript_1.DataType.JSONB, allowNull: true }),
    __metadata("design:type", Array)
], OffboardedEmployeeChecklist.prototype, "visibilityPermission", void 0);
OffboardedEmployeeChecklist = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'data_offboarded_employee_checklists',
    })
], OffboardedEmployeeChecklist);
exports.OffboardedEmployeeChecklist = OffboardedEmployeeChecklist;
//# sourceMappingURL=offboarded-employee-checklist.model.js.map