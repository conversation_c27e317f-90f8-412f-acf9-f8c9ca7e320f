import { BaseRepository } from "src/shared/repositories";
import { OffboardedEmployeeApprover } from "../models";
import { CurrentContext } from "src/shared/types";
export declare class OffboardingEmployeeApproverRepository extends BaseRepository<OffboardedEmployeeApprover> {
    constructor();
    createExitApprovers(payload: any, currentContext: CurrentContext): Promise<OffboardedEmployeeApprover[]>;
}
