import {
  FirstPage_default,
  LastPage_default
} from "./chunk-VQAZBO2N.js";
import {
  useSlot
} from "./chunk-RAWWVG2H.js";
import {
  ButtonBase_default
} from "./chunk-DNHML5ZA.js";
import {
  createSimplePaletteValueFilter
} from "./chunk-AACZXOME.js";
import {
  createSvgIcon
} from "./chunk-BQYUUIZF.js";
import {
  capitalize_default
} from "./chunk-LNQPTR5H.js";
import {
  memoTheme_default
} from "./chunk-D4UBMUUL.js";
import {
  useDefaultProps
} from "./chunk-LDNIHDC6.js";
import {
  styled_default
} from "./chunk-4RXV4KIM.js";
import {
  alpha,
  clsx_default,
  composeClasses,
  generateUtilityClass,
  generateUtilityClasses,
  useRtl
} from "./chunk-NXUIKIEE.js";
import {
  require_prop_types
} from "./chunk-J4LPPHPF.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/@mui/material/PaginationItem/PaginationItem.js
var React3 = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());

// node_modules/@mui/material/PaginationItem/paginationItemClasses.js
function getPaginationItemUtilityClass(slot) {
  return generateUtilityClass("MuiPaginationItem", slot);
}
var paginationItemClasses = generateUtilityClasses("MuiPaginationItem", ["root", "page", "sizeSmall", "sizeLarge", "text", "textPrimary", "textSecondary", "outlined", "outlinedPrimary", "outlinedSecondary", "rounded", "ellipsis", "firstLast", "previousNext", "focusVisible", "disabled", "selected", "icon", "colorPrimary", "colorSecondary"]);
var paginationItemClasses_default = paginationItemClasses;

// node_modules/@mui/material/internal/svg-icons/NavigateBefore.js
var React = __toESM(require_react());
var import_jsx_runtime = __toESM(require_jsx_runtime());
var NavigateBefore_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"
}), "NavigateBefore");

// node_modules/@mui/material/internal/svg-icons/NavigateNext.js
var React2 = __toESM(require_react());
var import_jsx_runtime2 = __toESM(require_jsx_runtime());
var NavigateNext_default = createSvgIcon((0, import_jsx_runtime2.jsx)("path", {
  d: "M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
}), "NavigateNext");

// node_modules/@mui/material/PaginationItem/PaginationItem.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime());
var overridesResolver = (props, styles) => {
  const {
    ownerState
  } = props;
  return [styles.root, styles[ownerState.variant], styles[`size${capitalize_default(ownerState.size)}`], ownerState.variant === "text" && styles[`text${capitalize_default(ownerState.color)}`], ownerState.variant === "outlined" && styles[`outlined${capitalize_default(ownerState.color)}`], ownerState.shape === "rounded" && styles.rounded, ownerState.type === "page" && styles.page, (ownerState.type === "start-ellipsis" || ownerState.type === "end-ellipsis") && styles.ellipsis, (ownerState.type === "previous" || ownerState.type === "next") && styles.previousNext, (ownerState.type === "first" || ownerState.type === "last") && styles.firstLast];
};
var useUtilityClasses = (ownerState) => {
  const {
    classes,
    color,
    disabled,
    selected,
    size,
    shape,
    type,
    variant
  } = ownerState;
  const slots = {
    root: ["root", `size${capitalize_default(size)}`, variant, shape, color !== "standard" && `color${capitalize_default(color)}`, color !== "standard" && `${variant}${capitalize_default(color)}`, disabled && "disabled", selected && "selected", {
      page: "page",
      first: "firstLast",
      last: "firstLast",
      "start-ellipsis": "ellipsis",
      "end-ellipsis": "ellipsis",
      previous: "previousNext",
      next: "previousNext"
    }[type]],
    icon: ["icon"]
  };
  return composeClasses(slots, getPaginationItemUtilityClass, classes);
};
var PaginationItemEllipsis = styled_default("div", {
  name: "MuiPaginationItem",
  slot: "Root",
  overridesResolver
})(memoTheme_default(({
  theme
}) => ({
  ...theme.typography.body2,
  borderRadius: 32 / 2,
  textAlign: "center",
  boxSizing: "border-box",
  minWidth: 32,
  padding: "0 6px",
  margin: "0 3px",
  color: (theme.vars || theme).palette.text.primary,
  height: "auto",
  [`&.${paginationItemClasses_default.disabled}`]: {
    opacity: (theme.vars || theme).palette.action.disabledOpacity
  },
  variants: [{
    props: {
      size: "small"
    },
    style: {
      minWidth: 26,
      borderRadius: 26 / 2,
      margin: "0 1px",
      padding: "0 4px"
    }
  }, {
    props: {
      size: "large"
    },
    style: {
      minWidth: 40,
      borderRadius: 40 / 2,
      padding: "0 10px",
      fontSize: theme.typography.pxToRem(15)
    }
  }]
})));
var PaginationItemPage = styled_default(ButtonBase_default, {
  name: "MuiPaginationItem",
  slot: "Root",
  overridesResolver
})(memoTheme_default(({
  theme
}) => ({
  ...theme.typography.body2,
  borderRadius: 32 / 2,
  textAlign: "center",
  boxSizing: "border-box",
  minWidth: 32,
  height: 32,
  padding: "0 6px",
  margin: "0 3px",
  color: (theme.vars || theme).palette.text.primary,
  [`&.${paginationItemClasses_default.focusVisible}`]: {
    backgroundColor: (theme.vars || theme).palette.action.focus
  },
  [`&.${paginationItemClasses_default.disabled}`]: {
    opacity: (theme.vars || theme).palette.action.disabledOpacity
  },
  transition: theme.transitions.create(["color", "background-color"], {
    duration: theme.transitions.duration.short
  }),
  "&:hover": {
    backgroundColor: (theme.vars || theme).palette.action.hover,
    // Reset on touch devices, it doesn't add specificity
    "@media (hover: none)": {
      backgroundColor: "transparent"
    }
  },
  [`&.${paginationItemClasses_default.selected}`]: {
    backgroundColor: (theme.vars || theme).palette.action.selected,
    "&:hover": {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),
      // Reset on touch devices, it doesn't add specificity
      "@media (hover: none)": {
        backgroundColor: (theme.vars || theme).palette.action.selected
      }
    },
    [`&.${paginationItemClasses_default.focusVisible}`]: {
      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.action.selectedChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.action.selected, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)
    },
    [`&.${paginationItemClasses_default.disabled}`]: {
      opacity: 1,
      color: (theme.vars || theme).palette.action.disabled,
      backgroundColor: (theme.vars || theme).palette.action.selected
    }
  },
  variants: [{
    props: {
      size: "small"
    },
    style: {
      minWidth: 26,
      height: 26,
      borderRadius: 26 / 2,
      margin: "0 1px",
      padding: "0 4px"
    }
  }, {
    props: {
      size: "large"
    },
    style: {
      minWidth: 40,
      height: 40,
      borderRadius: 40 / 2,
      padding: "0 10px",
      fontSize: theme.typography.pxToRem(15)
    }
  }, {
    props: {
      shape: "rounded"
    },
    style: {
      borderRadius: (theme.vars || theme).shape.borderRadius
    }
  }, {
    props: {
      variant: "outlined"
    },
    style: {
      border: theme.vars ? `1px solid rgba(${theme.vars.palette.common.onBackgroundChannel} / 0.23)` : `1px solid ${theme.palette.mode === "light" ? "rgba(0, 0, 0, 0.23)" : "rgba(255, 255, 255, 0.23)"}`,
      [`&.${paginationItemClasses_default.selected}`]: {
        [`&.${paginationItemClasses_default.disabled}`]: {
          borderColor: (theme.vars || theme).palette.action.disabledBackground,
          color: (theme.vars || theme).palette.action.disabled
        }
      }
    }
  }, {
    props: {
      variant: "text"
    },
    style: {
      [`&.${paginationItemClasses_default.selected}`]: {
        [`&.${paginationItemClasses_default.disabled}`]: {
          color: (theme.vars || theme).palette.action.disabled
        }
      }
    }
  }, ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(["dark", "contrastText"])).map(([color]) => ({
    props: {
      variant: "text",
      color
    },
    style: {
      [`&.${paginationItemClasses_default.selected}`]: {
        color: (theme.vars || theme).palette[color].contrastText,
        backgroundColor: (theme.vars || theme).palette[color].main,
        "&:hover": {
          backgroundColor: (theme.vars || theme).palette[color].dark,
          // Reset on touch devices, it doesn't add specificity
          "@media (hover: none)": {
            backgroundColor: (theme.vars || theme).palette[color].main
          }
        },
        [`&.${paginationItemClasses_default.focusVisible}`]: {
          backgroundColor: (theme.vars || theme).palette[color].dark
        },
        [`&.${paginationItemClasses_default.disabled}`]: {
          color: (theme.vars || theme).palette.action.disabled
        }
      }
    }
  })), ...Object.entries(theme.palette).filter(createSimplePaletteValueFilter(["light"])).map(([color]) => ({
    props: {
      variant: "outlined",
      color
    },
    style: {
      [`&.${paginationItemClasses_default.selected}`]: {
        color: (theme.vars || theme).palette[color].main,
        border: `1px solid ${theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / 0.5)` : alpha(theme.palette[color].main, 0.5)}`,
        backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / ${theme.vars.palette.action.activatedOpacity})` : alpha(theme.palette[color].main, theme.palette.action.activatedOpacity),
        "&:hover": {
          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / calc(${theme.vars.palette.action.activatedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette[color].main, theme.palette.action.activatedOpacity + theme.palette.action.focusOpacity),
          // Reset on touch devices, it doesn't add specificity
          "@media (hover: none)": {
            backgroundColor: "transparent"
          }
        },
        [`&.${paginationItemClasses_default.focusVisible}`]: {
          backgroundColor: theme.vars ? `rgba(${theme.vars.palette[color].mainChannel} / calc(${theme.vars.palette.action.activatedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette[color].main, theme.palette.action.activatedOpacity + theme.palette.action.focusOpacity)
        }
      }
    }
  }))]
})));
var PaginationItemPageIcon = styled_default("div", {
  name: "MuiPaginationItem",
  slot: "Icon",
  overridesResolver: (props, styles) => styles.icon
})(memoTheme_default(({
  theme
}) => ({
  fontSize: theme.typography.pxToRem(20),
  margin: "0 -8px",
  variants: [{
    props: {
      size: "small"
    },
    style: {
      fontSize: theme.typography.pxToRem(18)
    }
  }, {
    props: {
      size: "large"
    },
    style: {
      fontSize: theme.typography.pxToRem(22)
    }
  }]
})));
var PaginationItem = React3.forwardRef(function PaginationItem2(inProps, ref) {
  const props = useDefaultProps({
    props: inProps,
    name: "MuiPaginationItem"
  });
  const {
    className,
    color = "standard",
    component,
    components = {},
    disabled = false,
    page,
    selected = false,
    shape = "circular",
    size = "medium",
    slots = {},
    slotProps = {},
    type = "page",
    variant = "text",
    ...other
  } = props;
  const ownerState = {
    ...props,
    color,
    disabled,
    selected,
    shape,
    size,
    type,
    variant
  };
  const isRtl = useRtl();
  const classes = useUtilityClasses(ownerState);
  const externalForwardedProps = {
    slots: {
      previous: slots.previous ?? components.previous,
      next: slots.next ?? components.next,
      first: slots.first ?? components.first,
      last: slots.last ?? components.last
    },
    slotProps
  };
  const [PreviousSlot, previousSlotProps] = useSlot("previous", {
    elementType: NavigateBefore_default,
    externalForwardedProps,
    ownerState
  });
  const [NextSlot, nextSlotProps] = useSlot("next", {
    elementType: NavigateNext_default,
    externalForwardedProps,
    ownerState
  });
  const [FirstSlot, firstSlotProps] = useSlot("first", {
    elementType: FirstPage_default,
    externalForwardedProps,
    ownerState
  });
  const [LastSlot, lastSlotProps] = useSlot("last", {
    elementType: LastPage_default,
    externalForwardedProps,
    ownerState
  });
  const rtlAwareType = isRtl ? {
    previous: "next",
    next: "previous",
    first: "last",
    last: "first"
  }[type] : type;
  const IconSlot = {
    previous: PreviousSlot,
    next: NextSlot,
    first: FirstSlot,
    last: LastSlot
  }[rtlAwareType];
  const iconSlotProps = {
    previous: previousSlotProps,
    next: nextSlotProps,
    first: firstSlotProps,
    last: lastSlotProps
  }[rtlAwareType];
  return type === "start-ellipsis" || type === "end-ellipsis" ? (0, import_jsx_runtime3.jsx)(PaginationItemEllipsis, {
    ref,
    ownerState,
    className: clsx_default(classes.root, className),
    children: "…"
  }) : (0, import_jsx_runtime3.jsxs)(PaginationItemPage, {
    ref,
    ownerState,
    component,
    disabled,
    className: clsx_default(classes.root, className),
    ...other,
    children: [type === "page" && page, IconSlot ? (0, import_jsx_runtime3.jsx)(PaginationItemPageIcon, {
      ...iconSlotProps,
      className: classes.icon,
      as: IconSlot
    }) : null]
  });
});
true ? PaginationItem.propTypes = {
  // ┌────────────────────────────── Warning ──────────────────────────────┐
  // │ These PropTypes are generated from the TypeScript type definitions. │
  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │
  // └─────────────────────────────────────────────────────────────────────┘
  /**
   * @ignore
   */
  children: import_prop_types.default.node,
  /**
   * Override or extend the styles applied to the component.
   */
  classes: import_prop_types.default.object,
  /**
   * @ignore
   */
  className: import_prop_types.default.string,
  /**
   * The active color.
   * It supports both default and custom theme colors, which can be added as shown in the
   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).
   * @default 'standard'
   */
  color: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["primary", "secondary", "standard"]), import_prop_types.default.string]),
  /**
   * The component used for the root node.
   * Either a string to use a HTML element or a component.
   */
  component: import_prop_types.default.elementType,
  /**
   * The components used for each slot inside.
   *
   * This prop is an alias for the `slots` prop.
   * It's recommended to use the `slots` prop instead.
   *
   * @default {}
   * @deprecated use the `slots` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.
   */
  components: import_prop_types.default.shape({
    first: import_prop_types.default.elementType,
    last: import_prop_types.default.elementType,
    next: import_prop_types.default.elementType,
    previous: import_prop_types.default.elementType
  }),
  /**
   * If `true`, the component is disabled.
   * @default false
   */
  disabled: import_prop_types.default.bool,
  /**
   * The current page number.
   */
  page: import_prop_types.default.node,
  /**
   * If `true` the pagination item is selected.
   * @default false
   */
  selected: import_prop_types.default.bool,
  /**
   * The shape of the pagination item.
   * @default 'circular'
   */
  shape: import_prop_types.default.oneOf(["circular", "rounded"]),
  /**
   * The size of the component.
   * @default 'medium'
   */
  size: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["small", "medium", "large"]), import_prop_types.default.string]),
  /**
   * The props used for each slot inside.
   * @default {}
   */
  slotProps: import_prop_types.default.shape({
    first: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]),
    last: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]),
    next: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object]),
    previous: import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object])
  }),
  /**
   * The components used for each slot inside.
   * @default {}
   */
  slots: import_prop_types.default.shape({
    first: import_prop_types.default.elementType,
    last: import_prop_types.default.elementType,
    next: import_prop_types.default.elementType,
    previous: import_prop_types.default.elementType
  }),
  /**
   * The system prop that allows defining system overrides as well as additional CSS styles.
   */
  sx: import_prop_types.default.oneOfType([import_prop_types.default.arrayOf(import_prop_types.default.oneOfType([import_prop_types.default.func, import_prop_types.default.object, import_prop_types.default.bool])), import_prop_types.default.func, import_prop_types.default.object]),
  /**
   * The type of pagination item.
   * @default 'page'
   */
  type: import_prop_types.default.oneOf(["end-ellipsis", "first", "last", "next", "page", "previous", "start-ellipsis"]),
  /**
   * The variant to use.
   * @default 'text'
   */
  variant: import_prop_types.default.oneOfType([import_prop_types.default.oneOf(["outlined", "text"]), import_prop_types.default.string])
} : void 0;
var PaginationItem_default = PaginationItem;

export {
  getPaginationItemUtilityClass,
  paginationItemClasses_default,
  PaginationItem_default
};
//# sourceMappingURL=chunk-BFLRD47D.js.map
