import { Injectable } from '@nestjs/common';
import { AdminApiClient, EmployeeDataApiClient, HistoryApiClient } from 'src/shared/clients';
import { EmployeeListResponseDto, FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto } from '../dtos';
import { CurrentContext } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { HttpException } from 'src/shared/exceptions';
import { APPROVER_STATUS, HIERARCHY_ENTITY_TYPE, HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, HttpStatus, OFFBOARDING_STATUS, ROLES } from 'src/shared/enums';
import { DatabaseHelper, multiObjectToInstance, singleObjectToInstance } from 'src/shared/helpers';
import { BasicEmployeeDetailResponseDto } from 'src/employee-detail/dtos';
import { BusinessEntityRoleUserResponseDto } from 'src/business-entity/dtos';
import { Pagination } from 'src/core/pagination';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from '../repositories';
import { EmployeeDetailResponseDto } from '../dtos/response/emloyee-complete-detail-response.dto';

@Injectable()
export class EmployeeOffboardingService {
    constructor(
        private readonly adminApiClient: AdminApiClient,
        private readonly employeeDataApiClient: EmployeeDataApiClient,
        private readonly offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository,
        private readonly databaseHelper: DatabaseHelper,
        private readonly masterApproverRepository: MasterApproverRepository,
        private readonly masterRoleChecklistRepository: MasterRoleChecklistRepository,
        private readonly offboardingEmployeeApproverRepository: OffboardingEmployeeApproverRepository,
        private readonly offboardingEmployeeChecklistRepository: OffboardingEmployeeChecklistRepository,
        private readonly historyApiClient: HistoryApiClient
    ) { }

    public async getEmployeeOffboardingList(
        page: number = 1,
        limit: number = 10,
        orderBy: string = 'updatedOn',
        orderDirection: string = 'DESC',
        filterDto?: FilterRequestDto,
    ): Promise<PaginatedEmployeeListResponseDto> {

        const { rows, count } = await this.offboardedEmployeeDetailRepository.getEmployeeOffboardingListByFilter(
            filterDto,
            page,
            limit,
            orderBy,
            orderDirection
        );

        const records = multiObjectToInstance(EmployeeListResponseDto, rows, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });

        return new Pagination({ records, total: count });
    }

    public async initiateNewExit(
        newExitRequest: InitiateNewExitRequest,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {

        const { entityId, employeeEmailId, lastPhysicalWorkingDate, lastEmploymentDate, leavingGroup, transferringGroup, noticePeriod, noticeMonths, exitType, note } = newExitRequest;

        return await this.databaseHelper.startTransaction(async () => {

            const employeeDetail = await this.employeeDataApiClient.getEmployee(employeeEmailId);

            if (!employeeDetail) {
                throw new HttpException('Employee doesn\'t exist in oracle system.', HttpStatus.NOT_FOUND);
            }

            const offboardedEmployeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetail(employeeDetail.work_email);

            if (offboardedEmployeeDetail) {
                throw new HttpException('The employee exit process has already been initiated.', HttpStatus.CONFLICT);
            }

            const entityDetail = await this.adminApiClient.getBusinessEntityDetailsById(entityId);

            if (!entityDetail) {
                throw new HttpException('Entity does not exist.', HttpStatus.NOT_FOUND);
            }

            const bsaUserList = await this.adminApiClient.getUsersByRoleOfAnEntity(ROLES.BSA, entityId);

            if (!bsaUserList || bsaUserList.length === 0) {
                throw new HttpException('No BSA found for the selected entity.', HttpStatus.NOT_FOUND);
            }

            const payload = {
                employeeDetail: singleObjectToInstance(BasicEmployeeDetailResponseDto, employeeDetail),
                entityId: entityDetail.id,
                entityCode: entityDetail.code,
                entityTitle: entityDetail.full_name,
                bsaDetail: multiObjectToInstance(BusinessEntityRoleUserResponseDto, bsaUserList),
                lastPhysicalWorkingDate,
                lastEmploymentDate,
                leavingGroup,
                transferringGroup: transferringGroup,
                isNoticePeriodServed: noticePeriod,
                noticePeriod: noticeMonths,
                exitType,
                note,
                status: OFFBOARDING_STATUS.INITIATED,
                userStatus: 'Pending Checklist Clearance'
            }

            const newEntry = await this.offboardedEmployeeDetailRepository.initiateNewExit(payload, currentContext);

            const parentEntityDetail = await this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, HIERARCHY_ENTITY_TYPE.BUSINESS_UNIT);

            if (!parentEntityDetail) {
                throw new HttpException('Invalid depatment selected.', HttpStatus.NOT_FOUND);
            }

            // Step 1 - Get all Approvers and store
            await this.generateApprovers(newEntry.id, parentEntityDetail.id, currentContext);

            // Step 2 - Get all checklist and store
            await this.generateChecklist(newEntry.id, parentEntityDetail.id, currentContext);

            // Step 3 - Generate all checklist Tasks
            // Step 4 - Send email to all checklist approvers

            // Step 5 - Generate History
            await this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: newEntry.id,
                entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                action_performed: HISTORY_ACTION_TYPE.CREATE,
                action_date: new Date(),
                comments: 'Exit initiated',
                additional_info: newEntry
            });

            // Step 6 - Return response
            return {
                message: 'New exit initiated successfully.',
                data: newEntry
            };

        });
    }

    public async getEmployeeExitDetail(
        id: number
    ): Promise<any> {
        const employeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetailById(id);

        if (employeeDetail) {

            const { approvers, checklists, ...exitDetails } = employeeDetail;

            const groupedChecklist = checklists.reduce((acc, curr) => {
                acc[curr.groupDisplayName] = acc[curr.groupDisplayName] || [];
                acc[curr.groupDisplayName].push(curr);
                return acc;
            }, {});

            const sortedApprovers = approvers.sort((a, b) => a.approvalSequence - b.approvalSequence);

            const response = {
                exitDetails,
                approvers: sortedApprovers,
                checklists: groupedChecklist
            };

            return singleObjectToInstance(EmployeeDetailResponseDto, response);
        }

        throw new HttpException('Employee detail does not exist.', HttpStatus.NOT_FOUND);
    }

    private async generateApprovers(employeeId, entityId, currentContext) {
        const masterApprovers = await this.masterApproverRepository.getEntityMasterApprovers(entityId);

        const approverPayload = masterApprovers.map((approver) => ({
            offboardingEmployeeDetailId: employeeId,
            status: approver.approvalSequence === 1 ? APPROVER_STATUS.IN_PROGRESS : APPROVER_STATUS.NOT_INITIATED,
            assignedOn: approver.approvalSequence === 1 ? new Date() : null,
            approvalSequence: approver.approvalSequence,
            title: approver.title,
            isChecklistApprover: approver.isChecklistApprover,
            role: approver.role
        }));

        await this.offboardingEmployeeApproverRepository.createExitApprovers(approverPayload, currentContext);
    }

    private async generateChecklist(employeeId, entityId, currentContext) {
        const masterChecklists = await this.masterRoleChecklistRepository.getEntityMasterChecklist(entityId);

        const checklistPayload = masterChecklists.map((checklist) => ({
            offboardingEmployeeDetailId: employeeId,
            checklistTitle: checklist.checklistTitle,
            groupDisplayName: checklist.groupDisplayName,
            roleKey: checklist.roleKey,
            roleType: checklist.roleType,
            allowedActions: checklist.allowedActions,
            dependantChecklistCodes: checklist.dependantChecklistCodes,
            code: checklist.code,
            sla: checklist.sla,
            visibilityPermission: checklist.visibilityPermission,
            assignedOn: checklist.dependantChecklistCodes?.length ? null : new Date()
        }));

        await this.offboardingEmployeeChecklistRepository.createExitChecklists(checklistPayload, currentContext);
    }

}
