import { Injectable } from '@nestjs/common';
import { AdminApiClient, EmployeeDataApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { EmployeeListResponseDto, FilterRequestDto, InitiateNewExitRequest, PaginatedEmployeeListResponseDto, ReminderRequestDto } from '../dtos';
import { CreateTask, CurrentContext, TASK_ENTITY_TYPE, TASK_REL_URL } from 'src/shared/types';
import { MessageResponseDto } from 'src/shared/dtos';
import { HttpException } from 'src/shared/exceptions';
import { APPROVAL_TYPE, APPROVER_STATUS, EMAIL_TEMPLATES, HIERARCHY_ENTITY_TYPE, HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, HttpStatus, NOTIFICATION_ENTITY_TYPE, OFFBOARDING_STATUS, ROLES } from 'src/shared/enums';
import { DatabaseHelper, multiObjectToInstance, replaceUrlVariable, singleObjectToInstance } from 'src/shared/helpers';
import { BasicEmployeeDetailResponseDto } from 'src/employee-detail/dtos';
import { BusinessEntityRoleUserResponseDto } from 'src/business-entity/dtos';
import { Pagination } from 'src/core/pagination';
import { MasterApproverRepository, MasterRoleChecklistRepository, OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from '../repositories';
import { ChecklistResponse, EmployeeDetailResponseDto } from '../dtos/response/emloyee-complete-detail-response.dto';
import { ConfigService } from 'src/config/config.service';
import { TaskApiClient } from 'src/shared/clients/task-api.client';
import { flatten, toNumber } from 'lodash';
import { TaskService } from 'src/task/services';
import { SharedNotificationService } from 'src/shared/services';

@Injectable()
export class EmployeeOffboardingService {
    constructor(
        private readonly adminApiClient: AdminApiClient,
        private readonly employeeDataApiClient: EmployeeDataApiClient,
        private readonly offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository,
        private readonly databaseHelper: DatabaseHelper,
        private readonly masterApproverRepository: MasterApproverRepository,
        private readonly masterRoleChecklistRepository: MasterRoleChecklistRepository,
        private readonly offboardingEmployeeApproverRepository: OffboardingEmployeeApproverRepository,
        private readonly offboardingEmployeeChecklistRepository: OffboardingEmployeeChecklistRepository,
        private readonly historyApiClient: HistoryApiClient,
        private readonly mSGraphApiClient: MSGraphApiClient,
        private readonly configService: ConfigService,
        private readonly taskApiClient: TaskApiClient,
        private readonly taskService: TaskService,
        private readonly sharedNotificationService: SharedNotificationService,
    ) { }

    public async getEmployeeOffboardingList(
        page: number = 1,
        limit: number = 10,
        orderBy: string = 'updatedOn',
        orderDirection: string = 'DESC',
        filterDto?: FilterRequestDto,
    ): Promise<PaginatedEmployeeListResponseDto> {

        const { rows, count } = await this.offboardedEmployeeDetailRepository.getEmployeeOffboardingListByFilter(
            filterDto,
            page,
            limit,
            orderBy,
            orderDirection
        );

        const records = multiObjectToInstance(EmployeeListResponseDto, rows, {
            excludeExtraneousValues: true,
            enableImplicitConversion: true,
        });

        return new Pagination({ records, total: count });
    }

    public async initiateNewExit(
        newExitRequest: InitiateNewExitRequest,
        currentContext: CurrentContext,
    ): Promise<MessageResponseDto> {

        // leavingGroup, transferringGroup,
        const { entityId, employeeEmailId, lastPhysicalWorkingDate, lastEmploymentDate, noticePeriod, noticeMonths, exitType, note } = newExitRequest;

        return await this.databaseHelper.startTransaction(async () => {

            const userDetail = await this.mSGraphApiClient.getUserDetails(employeeEmailId);

            if (!userDetail) {
                throw new HttpException('Invalid User.', HttpStatus.NOT_FOUND);
            }

            const employeeDetail = await this.employeeDataApiClient.getEmployee([userDetail.mail, userDetail.userPrincipalName]);

            if (!employeeDetail?.work_email) {
                throw new HttpException('Employee doesn\'t exist.', HttpStatus.NOT_FOUND);
            }

            const offboardedEmployeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetail(employeeDetail.work_email);

            if (offboardedEmployeeDetail) {
                throw new HttpException('The employee exit process has already been initiated.', HttpStatus.CONFLICT);
            }

            const entityDetail = await this.adminApiClient.getBusinessEntityDetailsById(entityId);

            if (!entityDetail) {
                throw new HttpException('Entity does not exist.', HttpStatus.NOT_FOUND);
            }

            const bsaUserList = await this.adminApiClient.getUsersByRoleOfAnEntity(ROLES.BSA, entityId);

            if (!bsaUserList || bsaUserList.length === 0) {
                throw new HttpException('No BSA found for the selected entity.', HttpStatus.NOT_FOUND);
            }

            const payload = {
                employeeDetail: singleObjectToInstance(BasicEmployeeDetailResponseDto, employeeDetail),
                entityId: entityDetail.id,
                entityCode: entityDetail.code,
                entityTitle: entityDetail.full_name,
                bsaDetail: multiObjectToInstance(BusinessEntityRoleUserResponseDto, bsaUserList),
                lastPhysicalWorkingDate,
                lastEmploymentDate,
                // leavingGroup,
                // transferringGroup,
                isNoticePeriodServed: noticePeriod,
                noticePeriod: noticeMonths,
                exitType,
                note,
                status: OFFBOARDING_STATUS.INITIATED,
                userStatus: 'Pending Checklist Clearance'
            }

            const newEntry = await this.offboardedEmployeeDetailRepository.initiateNewExit(payload, currentContext);

            const parentEntityDetail = await this.adminApiClient.getParentEntityOfAnEntityOfGivenLevel(entityId, HIERARCHY_ENTITY_TYPE.BUSINESS_UNIT);

            if (!parentEntityDetail) {
                throw new HttpException('Invalid depatment selected.', HttpStatus.NOT_FOUND);
            }

            // Step 1 - Get all Approvers and store
            const generatedApprover = await this.generateApprovers(newEntry.id, parentEntityDetail.id, currentContext);

            // Step 2 - Get all checklist and store
            const generatedChecklists = await this.generateChecklist(newEntry.id, parentEntityDetail.id, currentContext);

            // Step 3 - Generate all checklist Tasks
            const generatedTask = await this.generateAllChecklistTask(generatedChecklists, generatedApprover, newEntry, entityId);

            // Step 4 - Send email to all checklist approvers
            await this.sendNotificationToChecklistApprovers(generatedTask, generatedChecklists, newEntry);

            // Step 5 - Generate History
            await this.historyApiClient.addRequestHistory({
                created_by: currentContext.user.username,
                entity_id: newEntry.id,
                entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
                action_performed: HISTORY_ACTION_TYPE.CREATE,
                action_date: new Date(),
                comments: 'Exit initiated',
                additional_info: newEntry
            });

            // Step 6 - Return response
            return {
                message: 'New exit initiated successfully.',
                data: newEntry
            };

        });
    }

    public async getEmployeeExitDetail(
        id: number,
        taskId: number,
        currentContext: CurrentContext,
    ): Promise<any> {

        if (taskId) {
            const { taskDetail } = await this.taskService.validateIfApproverTask(taskId, id, currentContext);

            if (!taskDetail) {
                throw new HttpException('Task not found.', HttpStatus.BAD_REQUEST);
            }
        }

        const employeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedEmployeeDetailById(id);

        if (employeeDetail) {

            const { approvers, checklists, ...exitDetails } = employeeDetail;

            const groupedChecklist = checklists.reduce((acc, curr) => {
                acc[curr.groupDisplayName] = acc[curr.groupDisplayName] || [];
                acc[curr.groupDisplayName].push(singleObjectToInstance(ChecklistResponse, curr));
                return acc;
            }, {});

            const sortedApprovers = approvers.sort((a, b) => a.approvalSequence - b.approvalSequence);

            let response: any = {
                exitDetails,
                approvers: sortedApprovers,
                checklists: groupedChecklist
            };

            if (taskId) {
                response = {
                    ...response,
                    approvalType: sortedApprovers.find((approver) => approver.status === APPROVER_STATUS.IN_PROGRESS)?.approvalType || APPROVAL_TYPE.APPROVAL
                };
            }

            return singleObjectToInstance(EmployeeDetailResponseDto, response);
        }

        throw new HttpException('Employee detail does not exist.', HttpStatus.NOT_FOUND);
    }

    public async sendReminder(reminderRequest: ReminderRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {

        return {
            message: 'Reminder sent successfully.'
        }

    }

    private async sendNotificationToChecklistApprovers(generatedTask, generatedChecklists, newEntry) {
        const groupedChecklist = generatedChecklists.reduce(
            (acc, curr) => {
                if (!acc[curr.roleKey]) acc[curr.roleKey] = [];
                acc[curr.roleKey].push(singleObjectToInstance(ChecklistResponse, curr));
                return acc;
            },
            {}
        );

        const groupedTask = generatedTask.reduce((acc, curr) => {
            acc[curr.assigned_to] = curr;
            return acc;
        }, {});

        const { uiClient } = this.configService.getAppConfig();

        // Instead of Object.entries + map → just use for...in
        const promises: Promise<any>[] = [];

        for (const roleKey in groupedChecklist) {
            const checklists = groupedChecklist[roleKey];

            const checklistsHtml = checklists
                .map((c) => `<li>${c.checklistTitle}</li>`)
                .join("");

            const usersPromise = this.getUsersEmailByRoleAndEntityId(
                roleKey,
                newEntry.entityId
            ).then((users) => {
                const task = groupedTask[roleKey];

                return this.sharedNotificationService.sendNotification(
                    newEntry.id,
                    NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK,
                    { to: users },
                    EMAIL_TEMPLATES.CHECKLIST_TASK,
                    {
                        employeeName: newEntry.employeeDetail.fullName,
                        employeeId: newEntry.employeeDetail.personNumber,
                        departmentName: checklists[0].groupDisplayName,
                        checklists: `<ul>${checklistsHtml}</ul>`,
                        taskLink: task
                            ? `<a href="${replaceUrlVariable(
                                `${uiClient.baseUrl}${TASK_REL_URL.CHECKLIST_TASK}`,
                                { taskId: task.task_id }
                            )}">Submit Checklist</a>`
                            : "",
                    }
                );
            });

            promises.push(usersPromise);
        }

        if (promises.length) {
            await Promise.all(promises);
        }
    }

    private async generateApprovers(employeeId, entityId, currentContext) {
        const masterApprovers = await this.masterApproverRepository.getEntityMasterApprovers(entityId);

        const approverPayload = masterApprovers.map((approver) => ({
            offboardingEmployeeDetailId: employeeId,
            status: approver.approvalSequence === 1 ? APPROVER_STATUS.IN_PROGRESS : APPROVER_STATUS.NOT_INITIATED,
            assignedOn: approver.approvalSequence === 1 ? new Date() : null,
            approvalSequence: approver.approvalSequence,
            title: approver.title,
            role: approver.role,
            approvalType: approver.approvalType
        }));

        console.log(approverPayload);

        return await this.offboardingEmployeeApproverRepository.createExitApprovers(approverPayload, currentContext);
    }

    private async generateChecklist(employeeId, entityId, currentContext) {
        const masterChecklists = await this.masterRoleChecklistRepository.getEntityMasterChecklist(entityId);

        const checklistPayload = masterChecklists.map((checklist) => ({
            offboardingEmployeeDetailId: employeeId,
            checklistTitle: checklist.checklistTitle,
            groupDisplayName: checklist.groupDisplayName,
            roleKey: checklist.roleKey,
            roleType: checklist.roleType,
            allowedActions: checklist.allowedActions,
            dependantChecklistCodes: checklist.dependantChecklistCodes,
            code: checklist.code,
            sla: checklist.sla,
            visibilityPermission: checklist.visibilityPermission,
            assignedOn: checklist.dependantChecklistCodes?.length ? null : new Date(),
            uniqueGroupId: checklist.uniqueGroupId
        }));

        return await this.offboardingEmployeeChecklistRepository.createExitChecklists(checklistPayload, currentContext);
    }

    private async generateAllChecklistTask(checklists: any, approvers: any, employeeDetail, entityId) {
        const rolesForTask = [];
        const taskPayloads: CreateTask[] = [];
        const { uiClient } = this.configService.getAppConfig();

        const checklistApproverDetail = approvers.find((approver) => approver.approvalType === APPROVAL_TYPE.CHECKLIST);

        checklists.forEach(async (checklist) => {

            if (!rolesForTask.includes(checklist.roleKey)) {
                taskPayloads.push({
                    title: `${employeeDetail.employeeDetail.fullName} - ${employeeDetail.employeeDetail.personNumber}`,
                    assigned_to: checklist.roleKey,
                    entity_type: TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK,
                    entity_id: this.taskService.generateTaskEntityId(employeeDetail.id, checklistApproverDetail?.id, checklist.uniqueGroupId),
                    is_group_assignemnt: true,
                    business_entity_id: entityId,
                    base_url: uiClient.baseUrl,
                    rel_url: TASK_REL_URL.CHECKLIST_TASK,
                    additional_info: {
                        ...employeeDetail.employeeDetail,
                        groupDisplayName: checklist.groupDisplayName,
                        exitId: toNumber(employeeDetail.id),
                        approverId: toNumber(checklistApproverDetail?.id),
                        groupId: toNumber(checklist.uniqueGroupId),
                        department: checklist.roleKey,
                        entityId: employeeDetail.entityId,
                        entityCode: employeeDetail.entityCode,
                        entityTitle: employeeDetail.entityTitle,
                        lastPhysicalWorkingDate: employeeDetail.lastPhysicalWorkingDate,
                        lastEmploymentDate: employeeDetail.lastEmploymentDate,
                        // leavingGroup: employeeDetail.leavingGroup,
                        // transferringGroup: employeeDetail.transferringGroup,
                        isNoticePeriodServed: employeeDetail.isNoticePeriodServed,
                        noticePeriod: employeeDetail.noticePeriod,
                        exitType: employeeDetail.exitType
                    }
                });

                rolesForTask.push(checklist.roleKey);
            }
        });

        console.log('taskPayloads', taskPayloads);


        const tasks = await Promise.all(
            taskPayloads.map((payload) =>
                this.taskApiClient.createTaskWithUseDelegation(payload)
            )
        );

        return flatten(tasks);
    }

    public async getUsersEmailByRoleAndEntityId(role: string, entityId: number): Promise<string[]> {
        const userEmails: string[] = [];
        const users = await this.adminApiClient.getUsersByRoleOfAnEntity(role, entityId);
        const userIds = users?.map(user => user.user_name.toLowerCase());
        const userAdDetails = userIds.length
            ? await this.mSGraphApiClient.getUsersDetails(userIds)
            : [];
        userEmails.push(...userAdDetails.map(user => user.mail));
        return userEmails;
    }

}
