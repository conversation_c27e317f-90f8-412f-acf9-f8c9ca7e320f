{"version": 3, "file": "offboarded-employee-approver.model.js", "sourceRoot": "", "sources": ["../../../src/employee-offboarding/models/offboarded-employee-approver.model.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+DAAsF;AACtF,gDAA8C;AAC9C,2FAA+E;AAC/E,kDAAiD;AACjD,8CAAmD;AAK5C,IAAM,0BAA0B,GAAhC,MAAM,0BAA2B,SAAQ,kBAAqC;CAsCpF,CAAA;AArCG;IAAC,IAAA,iCAAU,EAAC,GAAG,EAAE,CAAC,4DAAwB,CAAC;IAC1C,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,gCAAgC,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;+EAClD;AAE3C;IAAC,IAAA,gCAAS,EAAC,GAAG,EAAE,CAAC,4DAAwB,CAAC;8BACT,4DAAwB;4EAAC;AAE1D;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;yDAC/C;AAErB;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,IAAI,EAAE,+BAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC;;uEACtE;AAEpC;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;wDAC9C;AAEpB;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,mBAAmB,EAAE,IAAI,EAAE,+BAAQ,CAAC,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;;oEACjD;AAEhC;IAAC,IAAA,6BAAM,EAAC;QACJ,KAAK,EAAE,QAAQ;QACf,IAAI,EAAE,+BAAQ,CAAC,IAAI,CAAC,GAAG,IAAA,qBAAW,EAAC,uBAAe,CAAC,CAAC;QACpD,SAAS,EAAE,KAAK;KACnB,CAAC;;0DAC6B;AAE/B;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,SAAS,EAAE,IAAI,EAAE,+BAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;2DAC5C;AAEvB;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;8BACjD,IAAI;8DAAQ;AAEhC;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;8BACjD,IAAI;4DAAQ;AAE9B;IAAC,IAAA,6BAAM,EAAC,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,+BAAQ,CAAC,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC;;4DAC/C;AArCf,0BAA0B;IAHtC,IAAA,4BAAK,EAAC;QACH,SAAS,EAAE,oCAAoC;KAClD,CAAC;GACW,0BAA0B,CAsCtC;AAtCY,gEAA0B"}