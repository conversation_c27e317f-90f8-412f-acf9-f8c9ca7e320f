import { EXIT_TYPE_ENUM, OFFBOARDING_STATUS } from 'src/shared/enums';
import { BsaDetail } from 'src/shared/interfaces';
import { BaseModel } from 'src/shared/models';
import { EmployeeDetail } from 'src/shared/types';
import { OffboardedEmployeeApprover } from './offboarded-employee-approver.model';
import { OffboardedEmployeeChecklist } from './offboarded-employee-checklist.model';
export declare class OffboardedEmployeeDetail extends BaseModel<OffboardedEmployeeDetail> {
    employeeDetail: EmployeeDetail;
    entityId: number;
    entityCode: string;
    entityTitle: string;
    bsaDetail: BsaDetail[];
    lastPhysicalWorkingDate: Date;
    lastEmploymentDate: Date;
    leavingGroup: boolean;
    transferringGroup: boolean;
    isNoticePeriodServed: boolean;
    noticePeriod: number;
    exitType: EXIT_TYPE_ENUM;
    status: OFFBOARDING_STATUS;
    userStatus: string;
    note: string;
    approvers: OffboardedEmployeeApprover[];
    checklists: OffboardedEmployeeChecklist[];
}
