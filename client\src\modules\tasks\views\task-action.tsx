import ScrollToTopButton from '@/components/scroll-to-top';
import { useTranslate } from '@/locales/use-locales';
import EmployeeDetail from '@/modules/home/<USER>/details/employee-details';
import OffboardingDetailsCard from '@/modules/home/<USER>/details/offboarding-details-card';

import EmptyContent from '@/components/empty-content';
import LoadingScreen from '@/components/loading-screen/loading-screen';
import { getTaskDetails } from '@/shared/services';
import Grid from '@mui/material/Grid2';
import { Box } from '@mui/system';
import { toNumber } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { useParams } from 'react-router';
import { ActionChecklist } from '../components/actions';

const TaskAction = () => {
  const { t } = useTranslate();
  const { taskId } = useParams();
  const [errorMessage, setErrorMessage] = useState('');
  const {
    data: taskDetails,
    isFetching: _isTaskDetailFetching,
    isError,
  } = useQuery({
    queryKey: ['task-detail', toNumber(taskId)],
    queryFn: () => getTaskDetails(taskId ?? ''),
    onError: (error: any) => {
      setErrorMessage(typeof error?.message === 'string' ? error.message : t('error_messages.something_went_wrong'));
      enqueueSnackbar(typeof error?.message === 'string' ? error.message : t('error_messages.something_went_wrong'), {
        variant: 'error',
      });
    },
    keepPreviousData: false,
  });

  const employeeDetails = taskDetails?.exitDetail?.employeeDetail;
  const taskDetail = taskDetails?.taskDetail;
  const offboardingDetails = taskDetails?.exitDetail;
  const checkLists = taskDetails?.checklists ?? [];
  const dependantChecklist = taskDetails?.dependantChecklist ?? [];
  const exitId = taskDetails?.exitDetail.id;

  if (_isTaskDetailFetching) {
    return <LoadingScreen />;
  }

  if (isError && !taskDetail) {
    return (
      <EmptyContent
        filled={false}
        title={errorMessage}
        sx={{
          py: 7,
          flexShrink: 0,
          width: { xs: 1, md: 1 },
          minHeight: 300,
          position: 'relative',
          overflow: 'unset',
        }}
      />
    );
  }

  return (
    <Grid container spacing={2} mt={2} px={{ md: 2 }} width="100%">
      {/* ---------- FIRST ROW ---------- */}
      <Grid container spacing={2} width="100%" alignItems="stretch">
        <Grid size={{ xs: 12, lg: 8 }} sx={{ display: 'flex' }}>
          <Box sx={{ flex: 1, display: 'flex' }}>
            <OffboardingDetailsCard data={offboardingDetails} t={t} sx={{ flex: 1 }} />
          </Box>
        </Grid>
        <Grid size={{ xs: 12, lg: 4 }} sx={{ display: 'flex' }}>
          <Box sx={{ flex: 1, display: 'flex' }}>
            <EmployeeDetail employeeDetails={employeeDetails} sx={{ flex: 1 }} />
          </Box>
        </Grid>
      </Grid>

      {/* ---------- SECOND ROW (FULL WIDTH) ---------- */}
      <Grid size={{ xs: 12 }}>
        <ActionChecklist
          exitDetail={offboardingDetails}
          checkList={checkLists}
          taskDetail={taskDetail}
          dependantChecklist={dependantChecklist}
          taskId={toNumber(taskId)}
        />
      </Grid>

      <ScrollToTopButton />
    </Grid>
  );
};

export default TaskAction;
