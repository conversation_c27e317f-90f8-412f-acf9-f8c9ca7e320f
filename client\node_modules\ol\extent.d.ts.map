{"version": 3, "file": "extent.d.ts", "sourceRoot": "", "sources": ["extent.js"], "names": [], "mappings": "AAKA;;;;GAIG;AAEH;;;GAGG;AAEH;;;;;;GAMG;AACH,4CAJW,KAAK,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC,GAC1C,MAAM,CASjB;AAiBD;;;;;;;GAOG;AACH,+BANW,MAAM,SACN,MAAM,SACN,MAAM,GACL,MAAM,CAiBjB;AAED;;;;;;GAMG;AACH,8BAJW,MAAM,SACN,MAAM,GACL,MAAM,CAWjB;AAED;;;;;GAKG;AACH,iDALW,MAAM,KACN,MAAM,KACN,MAAM,GACL,MAAM,CAmBjB;AAED;;;;;;;GAOG;AACH,2CALW,MAAM,cACN,OAAO,iBAAiB,EAAE,UAAU,GACnC,OAAO,CAKlB;AAED;;;;;;;;;;;GAWG;AACH,wCANW,MAAM,WACN,MAAM,GACL,OAAO,CAWlB;AAED;;;;;;;;GAQG;AACH,mCANW,MAAM,KACN,MAAM,KACN,MAAM,GACL,OAAO,CAKlB;AAED;;;;;;GAMG;AACH,+CALW,MAAM,cACN,OAAO,iBAAiB,EAAE,UAAU,OA0B9C;AAED;;;;GAIG;AACH,+BAHY,MAAM,CAKjB;AAED;;;;;;;;GAQG;AACH,qCAPW,MAAM,QACN,MAAM,QACN,MAAM,QACN,MAAM,SACN,MAAM,GACL,MAAM,CAWjB;AAED;;;;GAIG;AACH,2CAHW,MAAM,GACL,MAAM,CAIjB;AAED;;;;GAIG;AACH,yDAJW,OAAO,iBAAiB,EAAE,UAAU,SACpC,MAAM,GACL,MAAM,CAMjB;AAED;;;;GAIG;AACH,2DAJW,KAAK,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC,SAC3C,MAAM,GACL,MAAM,CAKjB;AAED;;;;;;;GAOG;AACH,mEAPW,KAAK,CAAC,MAAM,CAAC,UACb,MAAM,OACN,MAAM,UACN,MAAM,SACN,MAAM,GACL,MAAM,CAWjB;AAED;;;;GAIG;AACH,+CAJW,KAAK,CAAC,KAAK,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC,CAAC,SAClD,MAAM,GACL,MAAM,CAKjB;AAED;;;;;;GAMG;AACH,gCALW,MAAM,WACN,MAAM,GACL,OAAO,CAUlB;AAED;;;;;;GAMG;AACH,6CALW,MAAM,WACN,MAAM,aACN,MAAM,GACL,OAAO,CASlB;AAED;;;;;;GAMG;AACH,gCALW,MAAM,WACN,MAAM,GACL,MAAM,CAiBjB;AAED;;;GAGG;AACH,yCAHW,MAAM,cACN,OAAO,iBAAiB,EAAE,UAAU,QAe9C;AAED;;;;GAIG;AACH,0CAJW,MAAM,eACN,KAAK,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC,GAC1C,MAAM,CAOjB;AAED;;;;;;;GAOG;AACH,8CAPW,MAAM,mBACN,KAAK,CAAC,MAAM,CAAC,UACb,MAAM,OACN,MAAM,UACN,MAAM,GACL,MAAM,CAajB;AAED;;;;GAIG;AACH,oCAJW,MAAM,SACN,KAAK,CAAC,KAAK,CAAC,OAAO,iBAAiB,EAAE,UAAU,CAAC,CAAC,GACjD,MAAM,CAOjB;AAED;;;;GAIG;AACH,iCAJW,MAAM,KACN,MAAM,KACN,MAAM,QAOhB;AAED;;;;;;;;GAQG;AACH,8BAFa,CAAC,UAHH,MAAM,YACN,CAAS,IAAoC,EAApC,OAAO,iBAAiB,EAAE,UAAU,KAAG,CAAC,GAChD,CAAC,GAAC,OAAO,CAsBpB;AAED;;;;;GAKG;AACH,gCAJW,MAAM,GACL,MAAM,CASjB;AAED;;;;;GAKG;AACH,sCAJW,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAK/C;AAED;;;;;GAKG;AACH,uCAJW,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAK/C;AAED;;;;;GAKG;AACH,kCAJW,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAK/C;AAED;;;;;GAKG;AACH,kCAJW,MAAM,UACN,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAgB/C;AAED;;;;GAIG;AACH,yCAJW,MAAM,WACN,MAAM,GACL,MAAM,CAQjB;AAED;;;;;;;GAOG;AACH,0CAPW,OAAO,iBAAiB,EAAE,UAAU,cACpC,MAAM,YACN,MAAM,QACN,OAAO,WAAW,EAAE,IAAI,SACxB,MAAM,GACL,MAAM,CAgBjB;AAED;;;;;;GAMG;AACH,2CANW,OAAO,iBAAiB,EAAE,UAAU,cACpC,MAAM,YACN,MAAM,QACN,OAAO,WAAW,EAAE,IAAI,GACvB,KAAK,CAAC,MAAM,CAAC,CAyBxB;AAED;;;;;GAKG;AACH,kCAJW,MAAM,GACL,MAAM,CAKjB;AAED;;;;GAIG;AACH,6CAJW,MAAM,WACN,MAAM,GACL,MAAM,CAKjB;AAED;;;;;;;GAOG;AACH,yCANW,MAAM,WACN,MAAM,SACN,MAAM,GACL,MAAM,CA8BjB;AAED;;;GAGG;AACH,kCAHW,MAAM,GACL,MAAM,CAIjB;AAED;;;;;GAKG;AACH,gCAJW,MAAM,GACL,OAAO,WAAW,EAAE,IAAI,CAKnC;AAED;;;;;GAKG;AACH,mCAJW,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAK/C;AAED;;;;;GAKG;AACH,oCAJW,MAAM,GACL,OAAO,iBAAiB,EAAE,UAAU,CAK/C;AAED;;;;;GAKG;AACH,iCAJW,MAAM,GACL,MAAM,CAKjB;AAED;;;;;;GAMG;AACH,oCALW,MAAM,WACN,MAAM,GACL,OAAO,CAUlB;AAED;;;;;GAKG;AACH,gCAJW,MAAM,GACL,OAAO,CAKlB;AAED;;;;GAIG;AACH,uCAJW,MAAM,SACN,MAAM,GACL,MAAM,CAWjB;AAED;;;GAGG;AACH,wCAHW,MAAM,SACN,MAAM,QAShB;AAED;;;;;;;GAOG;AACH,0CALW,MAAM,SACN,OAAO,iBAAiB,EAAE,UAAU,OACpC,OAAO,iBAAiB,EAAE,UAAU,GACnC,OAAO,CAwDlB;AAED;;;;;;;;;;GAUG;AACH,uCATW,MAAM,eACN,OAAO,WAAW,EAAE,iBAAiB,SAErC,MAAM,UACN,MAAM,GAEL,MAAM,CA2CjB;AAED;;;;;;;GAOG;AACH,8BAJW,MAAM,cACN,OAAO,sBAAsB,EAAE,OAAO,GACrC,MAAM,CAkBjB;AAED;;;;;;;;;;;;GAYG;AACH,sCALW,MAAM,cACN,OAAO,sBAAsB,EAAE,OAAO,eACtC,OAAO,GACN,KAAK,CAAC,MAAM,CAAC,CAkCxB;;;;qBAn5BY,KAAK,CAAC,MAAM,CAAC;;;;qBAMb,aAAa,GAAG,cAAc,GAAG,UAAU,GAAG,WAAW"}