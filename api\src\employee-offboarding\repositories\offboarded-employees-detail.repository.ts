import { Injectable } from "@nestjs/common";
import { BaseRepository } from "src/shared/repositories";
import { CurrentContext } from "src/shared/types";
import { OffboardedEmployeeApprover, OffboardedEmployeeChecklist, OffboardedEmployeeDetail } from "../models";
import { literal, Op, where } from "sequelize";

@Injectable()
export class OffboardedEmployeeDetailRepository extends BaseRepository<OffboardedEmployeeDetail> {
    constructor() {
        super(OffboardedEmployeeDetail);
    }

    public async initiateNewExit(
        payload: any,
        currentContext: CurrentContext,
    ): Promise<OffboardedEmployeeDetail> {
        const newExit = new OffboardedEmployeeDetail(payload);
        return this.save(newExit, currentContext);
    }

    public async getOffboardedEmployeeDetail(
        employeeEmailId: string,
    ): Promise<OffboardedEmployeeDetail> {
        const condition = {
            where: {
                employeeDetail: {
                    [Op.contains]: { workEmail: employeeEmailId.toLowerCase() }
                }
            }
        };
        return this.findOne(condition);
    }

    public async getOffboardedEmployeeDetailById(
        id: number,
    ): Promise<OffboardedEmployeeDetail> {
        return this.findOne({
            where: {
                id
            },
            include: [
                {
                    model: OffboardedEmployeeApprover,
                    as: 'approvers',
                    where: {
                        active: true,
                        deleted: false
                    }
                },
                {
                    model: OffboardedEmployeeChecklist,
                    as: 'checklists',
                    where: {
                        active: true,
                        deleted: false
                    }
                }
            ]
        });
    }

    public async getEmployeeOffboardingListByFilter(
        filterDto: any,
        page: number,
        limit: number,
        orderBy: string,
        orderDirection: string,
    ): Promise<{ rows: any[]; count: number }> {
        // const whereClause = this.buildWhereClause(filterDto);
        return this.findAndCountAll({
            // where: whereClause,
            ...(page && { offset: (page - 1) * limit }),
            ...(limit && { limit }),
            order: [[this.getOrderByColumn(orderBy), this.getOrderByDirection(orderDirection)]],
            // subQuery: false,
            distinct: true,
        });

    }

    private getOrderByColumn(orderBy: string) {
        switch (orderBy) {
            case 'status':
                return 'status';
            default:
                return 'updatedOn';
        }
    }

    private getOrderByDirection(orderByDirection: string) {
        switch (orderByDirection) {
            case 'asc':
            case 'ASC':
                return 'ASC';
            case 'desc':
            case 'DESC':
                return 'DESC';
            default:
                return 'DESC';
        }
    }

}