import { BaseRepository } from "src/shared/repositories";
import { OffboardedEmployeeChecklist } from "../models";
import { CurrentContext } from "src/shared/types";
export declare class OffboardingEmployeeChecklistRepository extends BaseRepository<OffboardedEmployeeChecklist> {
    constructor();
    createExitChecklists(payload: any, currentContext: CurrentContext): Promise<OffboardedEmployeeChecklist[]>;
}
