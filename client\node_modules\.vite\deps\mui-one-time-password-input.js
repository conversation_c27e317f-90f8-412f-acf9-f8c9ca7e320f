import {
  TextField_default
} from "./chunk-GLNPVPOL.js";
import "./chunk-R7DQFLYS.js";
import "./chunk-CUDZU3AR.js";
import "./chunk-PXXANCVM.js";
import "./chunk-NZ7VF6WQ.js";
import "./chunk-CAJ6DGUI.js";
import "./chunk-U3R3OX25.js";
import "./chunk-QXX2VII7.js";
import "./chunk-246E6JMT.js";
import "./chunk-GN3NL4H4.js";
import "./chunk-CPR6W5NF.js";
import "./chunk-4TQZGVIR.js";
import "./chunk-G6AIY4CT.js";
import "./chunk-ZOKU5DI5.js";
import "./chunk-DAWM2EV2.js";
import "./chunk-2YRDLZ64.js";
import "./chunk-I3G636XK.js";
import "./chunk-725EKR77.js";
import "./chunk-3HPN5KNE.js";
import "./chunk-PR6ZCO7G.js";
import "./chunk-TWWIGYGU.js";
import "./chunk-AKKANAVW.js";
import "./chunk-QDFPR22U.js";
import "./chunk-LA2HARY3.js";
import "./chunk-RAWWVG2H.js";
import {
  Box_default
} from "./chunk-LXM4NJOU.js";
import "./chunk-KO3MEAAR.js";
import "./chunk-HZOIS4LS.js";
import "./chunk-BFL632LT.js";
import "./chunk-A3Q7B7W4.js";
import "./chunk-4FTWOKSW.js";
import "./chunk-AACZXOME.js";
import "./chunk-NK3RFAWF.js";
import "./chunk-VGUODZZP.js";
import "./chunk-B3SVAE5P.js";
import "./chunk-FMQ4DQKR.js";
import "./chunk-JD6SWMFP.js";
import "./chunk-R7YEY7Q7.js";
import "./chunk-Z3ZIBUQO.js";
import "./chunk-FF5VZOPT.js";
import "./chunk-5ZZLQG6J.js";
import "./chunk-SNG3HHK6.js";
import "./chunk-JAIMNKAT.js";
import "./chunk-BQYUUIZF.js";
import "./chunk-YKGZIL4S.js";
import "./chunk-LNQPTR5H.js";
import "./chunk-D4UBMUUL.js";
import "./chunk-M3SFVNOA.js";
import "./chunk-LDNIHDC6.js";
import "./chunk-VIAU25KR.js";
import {
  styled_default
} from "./chunk-4RXV4KIM.js";
import "./chunk-NXUIKIEE.js";
import "./chunk-UUHLHOPM.js";
import "./chunk-JCOSHRX2.js";
import "./chunk-J4LPPHPF.js";
import "./chunk-OPLPMYTC.js";
import "./chunk-X53PWDJZ.js";
import "./chunk-EQCCHGRT.js";
import "./chunk-GS3CDLZ6.js";
import "./chunk-4JLRNKH6.js";
import "./chunk-SRNDHWC2.js";
import {
  require_jsx_runtime
} from "./chunk-OT5EQO2H.js";
import {
  require_react
} from "./chunk-OU5AQDZK.js";
import {
  __toESM
} from "./chunk-EWTE5DHJ.js";

// node_modules/mui-one-time-password-input/dist/mui-one-time-password-input.es.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var import_react = __toESM(require_react());
var G = styled_default(TextField_default)`
  input {
    text-align: center;
  }
`;
var J = {
  TextFieldStyled: G
};
var Q = (n) => (0, import_jsx_runtime.jsx)(J.TextFieldStyled, { ...n });
var D = {
  left: "ArrowLeft",
  right: "ArrowRight",
  backspace: "Backspace",
  home: "Home",
  end: "End"
};
function U(n, s) {
  return n <= 0 ? [] : Array.from({ length: n }, s);
}
function X(n, s, l) {
  return n.map((i, F) => s === F ? l : i);
}
function P(n) {
  return n.join("");
}
function M(n, s) {
  return [...n, s];
}
function Z(n, s, l) {
  return n.reduce(
    (i, F, C) => {
      const { characters: y, restArrayMerged: d } = i;
      if (C < l)
        return {
          restArrayMerged: d,
          characters: M(y, F)
        };
      const [V, ...E] = d;
      return {
        restArrayMerged: E,
        characters: M(y, V || "")
      };
    },
    {
      restArrayMerged: s,
      characters: []
    }
  ).characters;
}
function v(n) {
  return (s) => {
    n.forEach((l) => {
      typeof l == "function" ? l(s) : l != null && (l.current = s);
    });
  };
}
function ee(n) {
  return n.split("");
}
function N(n) {
  const s = import_react.default.useRef(() => {
    throw new Error("Cannot call an event handler while rendering.");
  });
  return import_react.default.useInsertionEffect(() => {
    s.current = n;
  }), import_react.default.useCallback((...l) => {
    var _a;
    return (_a = s.current) == null ? void 0 : _a.call(s, ...l);
  }, []);
}
var te = () => true;
var le = import_react.default.forwardRef(
  (n, s) => {
    const {
      value: l = "",
      length: i = 4,
      autoFocus: F = false,
      onChange: C,
      TextFieldsProps: y,
      onComplete: d,
      validateChar: V = te,
      className: E,
      onBlur: b,
      ...K
    } = n, j = import_react.default.useRef(l), w = N(d), I = N((e) => {
      const t = e.slice(0, i);
      return {
        isCompleted: t.length === i,
        finalValue: t
      };
    });
    import_react.default.useEffect(() => {
      const { isCompleted: e, finalValue: t } = I(
        j.current
      );
      e && w(t);
    }, [i, w, I]);
    const p = U(
      i,
      (e, t) => ({
        character: l[t] || "",
        inputRef: import_react.default.createRef()
      })
    ), T = (e) => p.findIndex(({ inputRef: t }) => t.current === e), k = () => p.map(({ character: e }) => e), A = (e, t) => {
      const r = X(
        k(),
        e,
        t
      );
      return P(r);
    }, $ = (e) => {
      var _a, _b;
      (_b = (_a = p[e]) == null ? void 0 : _a.inputRef.current) == null ? void 0 : _b.focus();
    }, c = (e) => {
      var _a, _b;
      (_b = (_a = p[e]) == null ? void 0 : _a.inputRef.current) == null ? void 0 : _b.select();
    }, O = (e) => {
      e + 1 !== i && (p[e + 1].character ? c(e + 1) : $(e + 1));
    }, S = (e, t) => typeof V != "function" ? true : V(e, t), Y = (e) => {
      const t = T(e.target);
      if (t === 0 && e.target.value.length > 1) {
        const { finalValue: m, isCompleted: B } = I(
          e.target.value
        );
        C == null ? void 0 : C(m), B && (d == null ? void 0 : d(m)), c(m.length - 1);
        return;
      }
      const r = e.target.value[0] || "";
      let u = r;
      u && !S(u, t) && (u = "");
      const a = A(t, u);
      C == null ? void 0 : C(a);
      const { isCompleted: h, finalValue: f } = I(a);
      h && (d == null ? void 0 : d(f)), u !== "" ? a.length - 1 < t ? c(a.length) : O(t) : r === "" && a.length <= t && c(t - 1);
    }, _ = (e) => {
      const t = e.target, r = t.selectionStart, u = t.selectionEnd, a = T(t), h = r === 0 && u === 0;
      if (t.value === e.key)
        e.preventDefault(), O(a);
      else if (D.backspace === e.key) {
        if (!t.value)
          e.preventDefault(), c(a - 1);
        else if (h) {
          e.preventDefault();
          const f = A(a, "");
          C == null ? void 0 : C(f), f.length <= a && c(a - 1);
        }
      } else D.left === e.key ? (e.preventDefault(), c(a - 1)) : D.right === e.key ? (e.preventDefault(), c(a + 1)) : D.home === e.key ? (e.preventDefault(), c(0)) : D.end === e.key && (e.preventDefault(), c(p.length - 1));
    }, H = (e) => {
      const t = e.clipboardData.getData("text/plain"), r = e.target, u = p.findIndex(
        ({ character: x, inputRef: o }) => x === "" || o.current === r
      ), a = k(), h = Z(
        a,
        ee(t),
        u
      ).map((x, o) => S(x, o) ? x : ""), f = P(h);
      C == null ? void 0 : C(f);
      const { isCompleted: m, finalValue: B } = I(f);
      m ? (d == null ? void 0 : d(B), c(i - 1)) : c(f.length);
    }, L = (e) => {
      if (!p.some(({ inputRef: r }) => r.current === e.relatedTarget)) {
        const { isCompleted: r, finalValue: u } = I(l);
        b == null ? void 0 : b(u, r);
      }
    };
    return (0, import_jsx_runtime.jsx)(
      Box_default,
      {
        display: "flex",
        gap: "20px",
        alignItems: "center",
        ref: s,
        className: `MuiOtpInput-Box ${E || ""}`,
        ...K,
        children: p.map(({ character: e, inputRef: t }, r) => {
          const {
            onPaste: u,
            onFocus: a,
            onKeyDown: h,
            className: f,
            onBlur: m,
            inputRef: B,
            ...x
          } = typeof y == "function" ? y(r) || {} : y || {};
          return (0, import_jsx_runtime.jsx)(
            Q,
            {
              autoFocus: F ? r === 0 : false,
              autoComplete: "one-time-code",
              value: e,
              inputRef: v([t, B]),
              className: `MuiOtpInput-TextField MuiOtpInput-TextField-${r + 1} ${f || ""}`,
              onPaste: (o) => {
                o.preventDefault(), H(o), u == null ? void 0 : u(o);
              },
              onFocus: (o) => {
                o.preventDefault(), o.target.select(), a == null ? void 0 : a(o);
              },
              onChange: Y,
              onKeyDown: (o) => {
                _(o), h == null ? void 0 : h(o);
              },
              onBlur: (o) => {
                m == null ? void 0 : m(o), L(o);
              },
              ...x
            },
            r
          );
        })
      }
    );
  }
);
export {
  le as MuiOtpInput
};
//# sourceMappingURL=mui-one-time-password-input.js.map
