import Iconify from '@/components/iconify';
import SvgColor from '@/components/svg-color';
import { useTranslate } from '@/locales/use-locales';
import { paths, ReplaceUrlVariable } from '@/routes/paths';
import { EmployeeDetails } from '@/shared/models/employee-exit-response.model';

import { ExtentionEnum, getIcon } from '@/shared/utils/get-icon';
import { Card, Link, Stack, Typography } from '@mui/material';
import { Box, Grid } from '@mui/system';
import { FC } from 'react';

interface EmployeeDetailProps {
  employeeDetails: EmployeeDetails | undefined;
  exitId?: string;
  sx: any;
}

const EmployeeDetail: FC<EmployeeDetailProps> = ({ sx, exitId, employeeDetails }) => {
  const { t } = useTranslate();

  const capitalizeName = (name: string): string => {
    return name
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <Card
      sx={{
        background: '#FFFFFF 0% 0% no-repeat padding-box',
        boxShadow: '0px 3px 6px #00000029',
        borderRadius: '17px ',
        opacity: 1,
        border: '1px solid #E8D6D6',
        px: 3,
        py: 2,
        ...sx,
      }}
    >
      <Grid container spacing={2}>
        {/* Heading */}
        <Grid size={12}>
          <Typography variant="mainTitle" fontWeight="bold" color="text.primary">
            {t('headings.employee_details')}
          </Typography>
        </Grid>

        {/* Employee Section */}
        <Grid size={12}>
          <Stack spacing={0.5}>
            <Typography variant="subtitle2" color="text.secondary">
              <SvgColor sx={{ height: 18, width: 18, mr: 0.5 }} src={getIcon('user', ExtentionEnum.PNG)} />
              {t('label.employee')}
            </Typography>
            <Typography variant="subtitle1" fontSize="16px">
              {capitalizeName(employeeDetails?.fullName || '')}{' '}
              {employeeDetails?.personNumber && (
                <Typography component="span" variant="body2" color="text.secondary">
                  ( Emp ID : {employeeDetails.personNumber} )
                </Typography>
              )}
            </Typography>
            {employeeDetails?.workEmail && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {/* Email Link */}
                <Link href={`mailto:${employeeDetails.workEmail}`} underline="hover" color="#6A1FDE" fontSize="0.9rem">
                  {employeeDetails.workEmail.toLowerCase()}
                </Link>

                {/* External Link Icon */}
                {exitId && (
                  <Link
                    href={ReplaceUrlVariable(paths.offboarding.employeeDetails, {
                      exitId,
                    })}
                    target="_blank"
                    rel="noopener noreferrer"
                    sx={{
                      flexShrink: 0,
                      color: 'text.secondary',
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <Iconify icon="mingcute:external-link-line" width={20} />
                  </Link>
                )}
              </Box>
            )}
            {employeeDetails?.jobPosition && (
              <Typography variant="body2" color="text.secondary">
                {employeeDetails.jobPosition}
              </Typography>
            )}
            {employeeDetails?.department && (
              <Typography variant="body2" color="text.secondary">
                {employeeDetails.department}
              </Typography>
            )}
          </Stack>
        </Grid>

        {/* Line Manager Section */}
        {(employeeDetails?.lineManagerName || employeeDetails?.lineManagerWorkEmail) && (
          <Grid size={12} mt={1}>
            <Stack spacing={0.5}>
              <Typography variant="subtitle2" color="text.secondary">
                <SvgColor sx={{ height: 18, width: 18, mr: 0.5 }} src={getIcon('Influencer')} />
                {t('label.line_manager')}
              </Typography>
              <Typography variant="subtitle1" fontSize="16px">
                {capitalizeName(employeeDetails?.lineManagerName || '')}
              </Typography>
              {employeeDetails?.lineManagerWorkEmail && (
                <Link
                  href={`mailto:${employeeDetails.lineManagerWorkEmail}`}
                  underline="hover"
                  color=" #6A1FDE"
                  fontSize="0.9rem"
                >
                  {employeeDetails.lineManagerWorkEmail.toLowerCase()}
                </Link>
              )}
            </Stack>
          </Grid>
        )}
      </Grid>
    </Card>
  );
};

export default EmployeeDetail;
