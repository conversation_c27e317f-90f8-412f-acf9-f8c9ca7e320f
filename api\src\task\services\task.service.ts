import { Injectable } from '@nestjs/common';
import { instanceTo<PERSON>lain } from 'class-transformer';
import _, { toNumber } from 'lodash';
import { AdminApiClient, HistoryApiClient, MSGraphApiClient } from 'src/shared/clients';
import { TaskApiClient } from 'src/shared/clients/task-api.client';
import { AD_USER_TYPE, APPROVAL_TYPE, APPROVER_ACTION, APPROVER_STATUS, EMAIL_TEMPLATES, HISTORY_ACTION_TYPE, HISTORY_ENTITY_TYPE, HttpStatus, NOTIFICATION_ENTITY_TYPE, OFFBOARDING_STATUS, PERMISSIONS, TASK_ACTION } from 'src/shared/enums';
import { HttpException } from 'src/shared/exceptions';
import { UserDetail } from 'src/shared/interfaces';
import { CurrentContext, TASK_ENTITY_TYPE, TASK_REL_URL } from 'src/shared/types';
import { ApproverActionRequestDto, ChecklistActionRequestDto, TaskActionDetailResponseDto, TaskDetailResponseDto } from '../dtos';
import { OffboardedEmployeeDetailRepository, OffboardingEmployeeApproverRepository, OffboardingEmployeeChecklistRepository } from 'src/employee-offboarding/repositories';
import { DatabaseHelper, replaceUrlVariable, singleObjectToInstance } from 'src/shared/helpers';
import { MessageResponseDto } from 'src/shared/dtos';
import { ConfigService } from 'src/config/config.service';
import { Op, Sequelize } from 'sequelize';
import { SharedNotificationService } from 'src/shared/services';
import { EmployeeOffboardingService } from 'src/employee-offboarding/services';

@Injectable()
export class TaskService {
	constructor(
		private readonly taskApiClient: TaskApiClient,
		private readonly adminApiClient: AdminApiClient,
		private readonly mSGraphApiClient: MSGraphApiClient,
		private readonly offboardedEmployeeDetailRepository: OffboardedEmployeeDetailRepository,
		private readonly checklistRepository: OffboardingEmployeeChecklistRepository,
		private readonly approverRepository: OffboardingEmployeeApproverRepository,
		private readonly configService: ConfigService,
		private readonly databaseHelper: DatabaseHelper,
		private readonly historyApiClient: HistoryApiClient,
		private readonly sharedNotificationService: SharedNotificationService,
		private readonly employeeOffboardingService: EmployeeOffboardingService,
	) { }

	/**
	 * Get user details from graph api.
	 * @param userId
	 * @returns
	 */
	private async getUserDetails(userId: string): Promise<UserDetail> {
		const {
			givenName: firstName,
			surname: lastName,
			userPrincipalName: upn,
			userType,
			mail: email,
			jobTitle,
		} = await this.mSGraphApiClient.getUserDetails(userId);
		const loginId = userType == AD_USER_TYPE.GUEST ? email.toLowerCase() : upn.toLowerCase();
		return { firstName, lastName, loginId, email, jobTitle };
	}

	/**
	 * Return all the pending tasks of an user.
	 * @param currentContext
	 * @returns
	 */
	public async getAllPendingUserTasks(
		currentContext: CurrentContext,
		assignedTo: string | null = null,
	): Promise<Record<string, any>> {
		let { username } = currentContext.user;

		if (assignedTo) {
			const hasAdminPermission = await this.adminApiClient.hasPermissionToUser(
				username,
				PERMISSIONS.APPLICATION_ADMIN,
			);

			if (!hasAdminPermission) {
				throw new HttpException(
					`You don't have permission to search task for other users.`,
					HttpStatus.UNAUTHORIZED,
				);
			}

			username = assignedTo;
		}

		const data = await this.taskApiClient.getAllPendingUserTasks(username);

		return data.map(d => instanceToPlain(new TaskDetailResponseDto(d), { excludeExtraneousValues: true }));
	}

	/**
	 * Get task details by its id.
	 * @param id
	 * @returns
	 */
	public async getTaskDetailById(id: number, currentContext: CurrentContext): Promise<Record<string, any>> {
		const taskDetail = await this.validateAndGetTaskDetail(id, currentContext);

		const { entity_type, additional_info } = taskDetail;
		const { exitId, groupId } = additional_info;

		if (entity_type === TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK) {

			const offboardingEmployeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedTaskEmployeeDetailById(exitId, groupId);

			if (!offboardingEmployeeDetail) {
				throw new HttpException(`Offboarding detail doesn't exist.`, HttpStatus.BAD_REQUEST);
			}

			if (offboardingEmployeeDetail.status !== OFFBOARDING_STATUS.IN_PROGRESS && offboardingEmployeeDetail.status !== OFFBOARDING_STATUS.INITIATED) {
				throw new HttpException(`Offboarding is already ${offboardingEmployeeDetail.status.toLowerCase()}.`, HttpStatus.BAD_REQUEST);
			}

			const { checklists, ...exitDetail } = offboardingEmployeeDetail;

			const dependentCodeList = [];
			checklists.forEach(checklist => {
				if (checklist?.dependantChecklistCodes?.length) {
					dependentCodeList.push(...checklist.dependantChecklistCodes);
				}
			});

			let dependantChecklist = [];
			if (dependentCodeList.length) {
				dependantChecklist = await this.checklistRepository.getExitChecklistByCodes(exitId, dependentCodeList);
			}

			return singleObjectToInstance(TaskActionDetailResponseDto, { exitDetail, checklists, taskDetail, dependantChecklist });
		}

		throw new HttpException(`Invalid request.`, HttpStatus.BAD_REQUEST);
	}

	public generateTaskEntityId(empId: number, approverId: number, groupId: number = 0): number {
		const str = `${empId}-${approverId}-${groupId}`;
		let hash = 0;

		for (let i = 0; i < str.length; i++) {
			hash = (hash * 31 + str.charCodeAt(i)) | 0; // force 32-bit signed int
		}

		return Math.abs(hash); // ensures positive integer
	}

	public async submitChecklistAction(payload: ChecklistActionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {

		const { taskId, checklistId, actions } = payload;

		return this.databaseHelper.startTransaction(async () => {
			const { uiClient } = this.configService.getAppConfig();

			// 1. Get task detail and validate permission and status.
			const taskDetail = await this.validateAndGetTaskDetail(taskId, currentContext);
			const { exitId, groupId } = taskDetail.additional_info;

			const historyPayload = [];

			// 2. Validate if offboarding exists.
			const offboardingEmployeeDetail = await this.offboardedEmployeeDetailRepository.getOffboardedChecklistDetailById(exitId, groupId, checklistId);

			const checklistDetail = this.validateOffboardingAndChecklist(offboardingEmployeeDetail, checklistId);

			// 4. If all ok store the action detail in checklist.
			await this.checklistRepository.updateChecklistActionById(checklistId, {
				actionBy: currentContext.user.username,
				actionOn: new Date(),
				actionDetail: actions,
			}, currentContext);

			//History added for checklist submission.
			historyPayload.push({
				created_by: currentContext.user.username,
				entity_id: exitId,
				entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
				action_performed: HISTORY_ACTION_TYPE.SUBMITTED,
				action_date: new Date(),
				comments: `Checklist submitted by ${checklistDetail.groupDisplayName.toLowerCase()} - ${checklistDetail.checklistTitle.toLowerCase()}.`,
				additional_info: actions
			});

			// 5. Find if all checklist is clear for that department and mark the task as completed for that department.
			let closeChecklistGroupTask = false;

			const allGroupSpecificPendingChecklist = await this.checklistRepository.pendingChecklistForExitGroup(exitId, groupId);

			// 6. Find if all checklist is clear for that exit id. 
			const allExitPendingChecklist = await this.checklistRepository.pendingChecklistForExit(exitId);

			// Find current approver
			const currentApprover = await this.approverRepository.getCurrentPendingApproverForExit(exitId);

			if (allGroupSpecificPendingChecklist.length === 0) {
				closeChecklistGroupTask = true;

				if (allExitPendingChecklist.length === 0) {

					if (!currentApprover) {
						throw new HttpException(`No pending approver found.`, HttpStatus.BAD_REQUEST);
					}

					// 6.1 Mark the current approver as approved.
					await this.approverRepository.updateApproverActionByCondition({
						id: currentApprover.id,
					}, {
						status: APPROVER_STATUS.APPROVED,
						actionBy: currentContext.user.username,
						actionOn: new Date(),
						comment: 'All checklist has been submitted.',
					}, currentContext);

					historyPayload.push({
						created_by: 'All Departments',
						entity_id: exitId,
						entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
						action_performed: HISTORY_ACTION_TYPE.APPROVED,
						action_date: new Date(),
						comments: 'All checklist has been submitted.',
						additional_info: {}
					});

					// Find the next approver
					const nextApprover = await this.approverRepository.getNextApproverForExit(exitId, currentApprover.approvalSequence);

					// If next approver exist then make it in progress and generate a new ticket for that.
					if (nextApprover) {
						await this.generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext);

						historyPayload.push({
							created_by: 'System',
							entity_id: exitId,
							entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
							action_performed: HISTORY_ACTION_TYPE.CREATE,
							action_date: new Date(),
							comments: `Task has been assigned to ${nextApprover.title.toLowerCase()} for approval.`,
							additional_info: {}
						});
					} else {
						// 6.4 if no more approver then Mark the exit completed.
						await this.markExitCompleted(offboardingEmployeeDetail, currentContext);

						historyPayload.push({
							created_by: 'System',
							entity_id: exitId,
							entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
							action_performed: HISTORY_ACTION_TYPE.APPROVED,
							action_date: new Date(),
							comments: `Exit offboarding completed.`,
							additional_info: {}
						});
					}
				} else {
					// 7. If all checklist not clear then check if any other checklist is dependent on this checklist and process.
					await this.findAndAdjustDependentChecklist(allExitPendingChecklist, checklistDetail.code, offboardingEmployeeDetail, currentApprover, currentContext);

					// If offboarding status is initiated and it means it is the first submittion so mark exit status to in progress from initiated status.
					if (offboardingEmployeeDetail.status === OFFBOARDING_STATUS.INITIATED) {
						await this.offboardedEmployeeDetailRepository.updateExitDetailById(
							exitId,
							{ status: OFFBOARDING_STATUS.IN_PROGRESS },
							currentContext
						);
					}
				}
			} else {
				await this.findAndAdjustDependentChecklist(allExitPendingChecklist, checklistDetail.code, offboardingEmployeeDetail, currentApprover, currentContext);
			}

			if (closeChecklistGroupTask) {
				await this.taskApiClient.completeTask({
					id: taskDetail.id,
					outcome: 'approved',
					created_by: currentContext.user.username,
					comments: 'All checklist completed.',
				});

				await this.sharedNotificationService.sendNotification(
					exitId,
					NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK,
					{ to: [offboardingEmployeeDetail.createdBy] },
					EMAIL_TEMPLATES.DEPARTMENT_CHECKLIST_CLEAR,
					{
						departmentName: checklistDetail.groupDisplayName,
						employeeName: offboardingEmployeeDetail.employeeDetail.fullName,
						employeeId: offboardingEmployeeDetail.employeeDetail.personNumber,
						offboardingDate: offboardingEmployeeDetail.createdOn.toDateString(),
						viewDetailLink: `<a href="${replaceUrlVariable(
							`${uiClient.baseUrl}${TASK_REL_URL.CHECKLIST_DETAIL}`,
							{ exitId }
						)}">View Offboarding Detail</a>`,
					}
				)
			}

			if (historyPayload.length) {
				await this.historyApiClient.addBulkRequestHistory(historyPayload);
			}

			return {
				message: 'Action submitted successfully.', data: {
					isTaskCompleted: closeChecklistGroupTask
				}
			};
		})
	}

	public async findAndAdjustDependentChecklist(allExitPendingChecklist, checklistCode, exitDetail, currentApprover, currentContext: CurrentContext) {
		// 7.1 Find if this checklist code exist for any dependant checklist which is not yet Submitted.
		const dependentChecklist = allExitPendingChecklist.filter((c) => c.dependantChecklistCodes?.includes(checklistCode));

		// 7.2 If it is available then find if it is single dependant available or multiple
		if (dependentChecklist.length) {

			let enableDependantChecklist = [];
			let removeDependantCodeFromList = [];

			dependentChecklist.forEach((checklist) => {
				const dependantCodeList = checklist.dependantChecklistCodes;

				if (dependantCodeList.length === 1) {
					enableDependantChecklist.push(checklist.id);
				}

				if (dependantCodeList.length > 1) {
					removeDependantCodeFromList.push(checklist);
				}
			});

			if (enableDependantChecklist.length) {
				// 7.2.1 If single mark it null and change the assigned date to current datetime and send email.
				await this.checklistRepository.updateChecklistByCondition(
					{ id: { [Op.in]: enableDependantChecklist } },
					{
						dependantChecklistCodes: null,
						assignedOn: new Date()
					},
					currentContext
				);

				let emailPromises = [];
				let { uiClient } = this.configService.getAppConfig();

				for (let i = 0; i < enableDependantChecklist.length; i++) {
					const checklistDetail = enableDependantChecklist[i];

					const currentTaskList = await this.taskApiClient.getAllTasks(
						this.generateTaskEntityId(exitDetail.id, currentApprover?.id, checklistDetail.uniqueGroupId),
						TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK,
					);

					const currentTaskDetail = currentTaskList.length ? currentTaskList[0] : null;

					const usersPromise = this.employeeOffboardingService.getUsersEmailByRoleAndEntityId(
						checklistDetail.roleKey,
						exitDetail.entityId
					).then((users) => {

						return this.sharedNotificationService.sendNotification(
							exitDetail.id,
							NOTIFICATION_ENTITY_TYPE.CHECKLIST_TASK,
							{ to: users },
							EMAIL_TEMPLATES.DEPENDANT_CHECKLIST_ASSIGNED,
							{
								employeeName: exitDetail.employeeDetail.fullName,
								employeeId: exitDetail.employeeDetail.personNumber,
								departmentName: checklistDetail.groupDisplayName,
								offboardingDate: exitDetail.createdOn.toDateString(),
								assignedOn: new Date().toDateString(),
								checklistName: checklistDetail.checklistTitle,
								taskLink: currentTaskDetail ? `<a href="${replaceUrlVariable(
									`${uiClient.baseUrl}${TASK_REL_URL.CHECKLIST_TASK}`,
									{ taskId: currentTaskDetail?.id }
								)}">Submit Checklist</a>` : `<a href="${uiClient.baseUrl + TASK_REL_URL.MY_TASK}">My Task</a>`,
							}
						);
					});

					emailPromises.push(usersPromise);
				}

				if (emailPromises.length) {
					await Promise.all(emailPromises);
				}
			}

			if (removeDependantCodeFromList.length) {
				// 7.2.2 If multiple then just remove that dependent code from there list.
				const removeDependantCodeUpdate = removeDependantCodeFromList.map((dependantChecklist) => {
					const updatedCodes = (dependantChecklist.dependantChecklistCodes || []).filter(
						(code) => code !== checklistCode
					);

					return this.checklistRepository.updateChecklistByCondition(
						{ id: dependantChecklist.id },
						{ dependantChecklistCodes: updatedCodes },
						currentContext
					);
				});

				// run all updates in parallel
				if (removeDependantCodeUpdate.length) {
					await Promise.all(removeDependantCodeUpdate);
				}
			}
		}
	}

	public async approverAction(payload: ApproverActionRequestDto, currentContext: CurrentContext): Promise<MessageResponseDto> {
		// 1. First add task id in detail page api to get task detail also in case of task id and validate task also. then come to this function for action.

		const { taskId, action, comment } = payload;

		const { taskDetail, currentApprover } = await this.validateIfApproverTask(taskId, null, currentContext);

		const { additional_info } = taskDetail;
		const { exitId } = additional_info;
		const { id: approverId } = currentApprover;
		const historyPayload = [];

		const offboardingEmployeeDetail = await this.offboardedEmployeeDetailRepository.getEmployeeDetailById(exitId);

		if (!offboardingEmployeeDetail) {
			throw new HttpException(`Exit detail not found.`, HttpStatus.BAD_REQUEST);
		}

		if (offboardingEmployeeDetail.status !== OFFBOARDING_STATUS.IN_PROGRESS) {
			throw new HttpException(`Offboarding is not currently in the In-Progress state.`, HttpStatus.BAD_REQUEST);
		}

		// 1. Close the task.
		await this.taskApiClient.completeTask({
			id: taskDetail.id,
			outcome: action.toLowerCase(),
			created_by: currentContext.user.username,
			comments: comment,
		});

		// SEND EMAIL NOTIFICATION [ To submitter that all checklist submitted by that group.] TODO

		// 2. Mark the current approver as approved.
		await this.approverRepository.updateApproverActionByCondition({
			id: approverId,
		}, {
			status: action,
			actionBy: currentContext.user.username,
			actionOn: new Date(),
			comment,
		}, currentContext);

		historyPayload.push({
			created_by: currentContext.user.username,
			entity_id: exitId,
			entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
			action_performed: HISTORY_ACTION_TYPE.ACTION,
			action_date: new Date(),
			comments: `${action.toLowerCase()} by ${currentApprover.title.toLowerCase()}.`,
			additional_info: {
				comment
			}
		});

		if (action === APPROVER_ACTION.APPROVED) {

			// Find the next approver
			const nextApprover = await this.approverRepository.getNextApproverForExit(exitId, currentApprover.approvalSequence);

			// If next approver exist then make it in progress and generate a new ticket for that.
			if (nextApprover) {

				await this.generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext);

				historyPayload.push({
					created_by: 'System',
					entity_id: exitId,
					entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
					action_performed: HISTORY_ACTION_TYPE.CREATE,
					action_date: new Date(),
					comments: `Task has been assigned to ${nextApprover.title.toLowerCase()} for approval.`,
					additional_info: {}
				});

			} else {

				//If no approver available then mark the exit as completed.
				await this.markExitCompleted(offboardingEmployeeDetail, currentContext);

				historyPayload.push({
					created_by: 'System',
					entity_id: exitId,
					entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
					action_performed: HISTORY_ACTION_TYPE.APPROVED,
					action_date: new Date(),
					comments: `Exit offboarding completed.`,
					additional_info: {}
				});
			}
		} else {
			// 3. If rejected then mark the exit as rejected.
			await this.offboardedEmployeeDetailRepository.updateExitDetailById(
				exitId,
				{ status: OFFBOARDING_STATUS.REJECTED, userStatus: 'Rejected' },
				currentContext
			);

			historyPayload.push({
				created_by: 'System',
				entity_id: exitId,
				entity_type: HISTORY_ENTITY_TYPE.EXIT_EMPLOYEE,
				action_performed: HISTORY_ACTION_TYPE.REJECTED,
				action_date: new Date(),
				comments: `Exit offboarding rejected.`,
				additional_info: {}
			});
		}

		if (historyPayload.length) {
			await this.historyApiClient.addBulkRequestHistory(historyPayload);
		}

		return {
			message: `Exit successfully ${action.toLowerCase()}.`,
		}
	}

	private async markExitCompleted(offboardingEmployeeDetail, currentContext: CurrentContext) {

		const { id: exitId } = offboardingEmployeeDetail;

		await this.offboardedEmployeeDetailRepository.updateExitDetailById(
			exitId,
			{
				status: OFFBOARDING_STATUS.APPROVED,
				userStatus: 'Completed'
			},
			currentContext
		);

		// SEND FINAL EMAIL NOTIFICATION FOR OFFBOARING COMPLETED AND APPROVED [TODO]
	}

	private async generateNewTaskForNextApprover(nextApprover, offboardingEmployeeDetail, currentContext: CurrentContext) {

		await this.approverRepository.updateApproverActionByCondition({
			id: nextApprover.id,
		}, {
			status: APPROVER_STATUS.IN_PROGRESS,
			assignedOn: new Date(),
		}, currentContext);

		const { uiClient } = this.configService.getAppConfig();

		// 6.3 Generate task for next approver.
		const taskPayload = {
			title: `${offboardingEmployeeDetail.employeeDetail.fullName} - ${offboardingEmployeeDetail.employeeDetail.personNumber}`,
			assigned_to: nextApprover.role,
			entity_type: TASK_ENTITY_TYPE.EOP_APPROVER_TASK,
			entity_id: this.generateTaskEntityId(offboardingEmployeeDetail.id, nextApprover?.id),
			is_group_assignemnt: true,
			business_entity_id: offboardingEmployeeDetail.entityId,
			base_url: uiClient.baseUrl,
			rel_url: TASK_REL_URL.APPROVER_TASK,
			additional_info: {
				...offboardingEmployeeDetail.employeeDetail,
				exitId: toNumber(offboardingEmployeeDetail.id),
				approverId: toNumber(nextApprover?.id),
				groupDisplayName: nextApprover.title,
				entityId: offboardingEmployeeDetail.entityId,
				entityCode: offboardingEmployeeDetail.entityCode,
				entityTitle: offboardingEmployeeDetail.entityTitle,
				lastPhysicalWorkingDate: offboardingEmployeeDetail.lastPhysicalWorkingDate,
				lastEmploymentDate: offboardingEmployeeDetail.lastEmploymentDate,
				isNoticePeriodServed: offboardingEmployeeDetail.isNoticePeriodServed,
				noticePeriod: offboardingEmployeeDetail.noticePeriod,
				exitType: offboardingEmployeeDetail.exitType
			}
		};

		await this.taskApiClient.createTaskWithUseDelegation(taskPayload);

		// SEND EMAIL FOR NEW TICKET GENERATED TO GROUP. [TODO]

		// Update the exit detail with user status as new approver assigned.
		await this.offboardedEmployeeDetailRepository.updateExitDetailById(
			offboardingEmployeeDetail.id,
			{ userStatus: `Waiting for ${nextApprover.title.toLowerCase()}.` },
			currentContext
		);
	}

	private async validateAndGetTaskDetail(taskId, currentContext: CurrentContext) {
		const taskDetail = await this.taskApiClient.getTaskById(taskId);

		if (!taskDetail || taskDetail?.task_status !== 'Not Started') {
			throw new HttpException(`Task doesn't exist.`, HttpStatus.BAD_REQUEST);
		}

		if (taskDetail?.entity_type !== TASK_ENTITY_TYPE.EOP_CHECKLIST_TASK) {
			throw new HttpException(`Task details are restricted to the Department Checklist Clearance role.`, HttpStatus.BAD_REQUEST);
		}

		const isTaskBelongToUser = await this.isTaskBelongToUser(taskDetail, currentContext.user.username);

		if (!isTaskBelongToUser) {
			throw new HttpException(`You are not authorized to view this task.`, HttpStatus.UNAUTHORIZED);
		}

		return taskDetail
	}

	private validateOffboardingAndChecklist(offboardingEmployeeDetail, checklistId) {
		if (!offboardingEmployeeDetail) {
			throw new HttpException(`Checklist not found for this department task.`, HttpStatus.BAD_REQUEST);
		}

		if (offboardingEmployeeDetail.status !== OFFBOARDING_STATUS.IN_PROGRESS && offboardingEmployeeDetail.status !== OFFBOARDING_STATUS.INITIATED) {
			throw new HttpException(`Offboarding is already ${offboardingEmployeeDetail.status.toLowerCase()}.`, HttpStatus.BAD_REQUEST);
		}

		// 3. Validate if that checklist id is valid for that offboarding detail and not yet submitted.
		const checklist = offboardingEmployeeDetail.checklists.find((c) => toNumber(c.id) === toNumber(checklistId));

		if (!checklist) {
			throw new HttpException(`Checklist is not associated with this task.`, HttpStatus.BAD_REQUEST);
		}

		if (checklist.actionOn) {
			throw new HttpException(`Checklist is already submitted.`, HttpStatus.BAD_REQUEST);
		}

		return checklist;
	}

	public async isTaskBelongToUser(taskDetail: any, username: string): Promise<boolean> {
		const { assigned_to, business_entity_id } = taskDetail;

		const isUserInGroup = await this.adminApiClient.hasUserRole(
			username,
			assigned_to,
			business_entity_id,
		);
		if (!isUserInGroup) {
			throw new HttpException(
				`User doesn't has permission to this task.`,
				HttpStatus.FORBIDDEN,
			);
		}

		return true;
	}

	public async validateIfApproverTask(taskId, exitId, currentContext: CurrentContext) {
		const taskDetail = await this.taskApiClient.getTaskById(taskId);

		if (!taskDetail || taskDetail?.task_status !== 'Not Started') {
			throw new HttpException(`Task doesn't exist.`, HttpStatus.BAD_REQUEST);
		}

		if (taskDetail?.entity_type !== TASK_ENTITY_TYPE.EOP_APPROVER_TASK) {
			throw new HttpException(`Task details are restricted to the approvers only.`, HttpStatus.BAD_REQUEST);
		}

		if (!exitId) {
			exitId = toNumber(taskDetail?.additional_info?.exitId);
		} else {
			if (!taskDetail?.additional_info?.exitId || taskDetail?.additional_info?.exitId !== exitId) {
				throw new HttpException('Task does not belong to this exit.', HttpStatus.BAD_REQUEST);
			}
		}

		const currentApprover = await this.approverRepository.getCurrentPendingApproverForExit(exitId);

		if (!currentApprover) {
			throw new HttpException('No pending approver found.', HttpStatus.BAD_REQUEST);
		}

		if (currentApprover.role !== taskDetail.assigned_to) {
			throw new HttpException('Exit is not pending with this approver.', HttpStatus.BAD_REQUEST);
		}

		if (currentApprover.approvalType === APPROVAL_TYPE.CHECKLIST) {
			throw new HttpException('Checklist task approval is restricted in this section.', HttpStatus.BAD_REQUEST);
		}

		const isTaskBelongToUser = await this.isTaskBelongToUser(taskDetail, currentContext.user.username);

		if (!isTaskBelongToUser) {
			throw new HttpException(`You are not authorized to view this task.`, HttpStatus.UNAUTHORIZED);
		}

		return { taskDetail, currentApprover };
	}
}
