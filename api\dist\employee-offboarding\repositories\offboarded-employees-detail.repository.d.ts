import { BaseRepository } from "src/shared/repositories";
import { CurrentContext } from "src/shared/types";
import { OffboardedEmployeeDetail } from "../models";
export declare class OffboardedEmployeeDetailRepository extends BaseRepository<OffboardedEmployeeDetail> {
    constructor();
    initiateNewExit(payload: any, currentContext: CurrentContext): Promise<OffboardedEmployeeDetail>;
    getOffboardedEmployeeDetail(employeeEmailId: string): Promise<OffboardedEmployeeDetail>;
    getOffboardedEmployeeDetailById(id: number): Promise<OffboardedEmployeeDetail>;
    getEmployeeOffboardingListByFilter(filterDto: any, page: number, limit: number, orderBy: string, orderDirection: string): Promise<{
        rows: any[];
        count: number;
    }>;
    private getOrderByColumn;
    private getOrderByDirection;
}
