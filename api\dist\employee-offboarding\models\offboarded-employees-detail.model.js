"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OffboardedEmployeeDetail = void 0;
const sequelize_typescript_1 = require("sequelize-typescript");
const enums_1 = require("../../shared/enums");
const helpers_1 = require("../../shared/helpers");
const models_1 = require("../../shared/models");
const offboarded_employee_approver_model_1 = require("./offboarded-employee-approver.model");
const offboarded_employee_checklist_model_1 = require("./offboarded-employee-checklist.model");
let OffboardedEmployeeDetail = class OffboardedEmployeeDetail extends models_1.BaseModel {
};
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'employee_detail', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Object)
], OffboardedEmployeeDetail.prototype, "employeeDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_id', type: sequelize_typescript_1.DataType.NUMBER, allowNull: false }),
    __metadata("design:type", Number)
], OffboardedEmployeeDetail.prototype, "entityId", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_code', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "entityCode", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'entity_title', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "entityTitle", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'bsa_detail', type: sequelize_typescript_1.DataType.JSONB, allowNull: false }),
    __metadata("design:type", Array)
], OffboardedEmployeeDetail.prototype, "bsaDetail", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'last_physical_working_date', type: sequelize_typescript_1.DataType.DATE, allowNull: false }),
    __metadata("design:type", Date)
], OffboardedEmployeeDetail.prototype, "lastPhysicalWorkingDate", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'last_employment_date', type: sequelize_typescript_1.DataType.DATE, allowNull: false }),
    __metadata("design:type", Date)
], OffboardedEmployeeDetail.prototype, "lastEmploymentDate", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'leaving_group', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: true }),
    __metadata("design:type", Boolean)
], OffboardedEmployeeDetail.prototype, "leavingGroup", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'transferring_group', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: true }),
    __metadata("design:type", Boolean)
], OffboardedEmployeeDetail.prototype, "transferringGroup", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'is_notice_period_served', type: sequelize_typescript_1.DataType.BOOLEAN, allowNull: false }),
    __metadata("design:type", Boolean)
], OffboardedEmployeeDetail.prototype, "isNoticePeriodServed", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'notice_period', type: sequelize_typescript_1.DataType.NUMBER, allowNull: true }),
    __metadata("design:type", Number)
], OffboardedEmployeeDetail.prototype, "noticePeriod", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'exit_type',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.EXIT_TYPE_ENUM)),
        allowNull: false
    }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "exitType", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({
        field: 'status',
        type: sequelize_typescript_1.DataType.ENUM(...(0, helpers_1.enumToArray)(enums_1.OFFBOARDING_STATUS)),
        allowNull: false
    }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "status", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'user_status', type: sequelize_typescript_1.DataType.STRING, allowNull: false }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "userStatus", void 0);
__decorate([
    (0, sequelize_typescript_1.Column)({ field: 'note', type: sequelize_typescript_1.DataType.TEXT, allowNull: true }),
    __metadata("design:type", String)
], OffboardedEmployeeDetail.prototype, "note", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => offboarded_employee_approver_model_1.OffboardedEmployeeApprover),
    __metadata("design:type", Array)
], OffboardedEmployeeDetail.prototype, "approvers", void 0);
__decorate([
    (0, sequelize_typescript_1.HasMany)(() => offboarded_employee_checklist_model_1.OffboardedEmployeeChecklist),
    __metadata("design:type", Array)
], OffboardedEmployeeDetail.prototype, "checklists", void 0);
OffboardedEmployeeDetail = __decorate([
    (0, sequelize_typescript_1.Table)({
        tableName: 'data_offboarded_employee_details',
    })
], OffboardedEmployeeDetail);
exports.OffboardedEmployeeDetail = OffboardedEmployeeDetail;
//# sourceMappingURL=offboarded-employees-detail.model.js.map