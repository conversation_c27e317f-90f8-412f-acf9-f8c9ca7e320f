{"version": 3, "sources": ["../../@mui/material/AvatarGroup/AvatarGroup.js", "../../@mui/material/Avatar/Avatar.js", "../../@mui/material/internal/svg-icons/Person.js", "../../@mui/material/Avatar/avatarClasses.js", "../../@mui/material/AvatarGroup/avatarGroupClasses.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { isFragment } from 'react-is';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Avatar, { avatarClasses } from \"../Avatar/index.js\";\nimport avatarGroupClasses, { getAvatarGroupUtilityClass } from \"./avatarGroupClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst SPACINGS = {\n  small: -16,\n  medium: -8\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root'],\n    avatar: ['avatar']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, classes);\n};\nconst AvatarGroupRoot = styled('div', {\n  name: 'MuiAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    return [{\n      [`& .${avatarGroupClasses.avatar}`]: styles.avatar\n    }, styles.root];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  flexDirection: 'row-reverse',\n  [`& .${avatarClasses.root}`]: {\n    border: `2px solid ${(theme.vars || theme).palette.background.default}`,\n    boxSizing: 'content-box',\n    marginLeft: 'var(--AvatarGroup-spacing, -8px)',\n    '&:last-child': {\n      marginLeft: 0\n    }\n  }\n})));\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatarGroup'\n  });\n  const {\n    children: childrenProp,\n    className,\n    component = 'div',\n    componentsProps,\n    max = 5,\n    renderSurplus,\n    slotProps = {},\n    slots = {},\n    spacing = 'medium',\n    total,\n    variant = 'circular',\n    ...other\n  } = props;\n  let clampedMax = max < 2 ? 2 : max;\n  const ownerState = {\n    ...props,\n    max,\n    spacing,\n    component,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  const children = React.Children.toArray(childrenProp).filter(child => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (isFragment(child)) {\n        console.error([\"MUI: The AvatarGroup component doesn't accept a Fragment as a child.\", 'Consider providing an array instead.'].join('\\n'));\n      }\n    }\n    return /*#__PURE__*/React.isValidElement(child);\n  });\n  const totalAvatars = total || children.length;\n  if (totalAvatars === clampedMax) {\n    clampedMax += 1;\n  }\n  clampedMax = Math.min(totalAvatars + 1, clampedMax);\n  const maxAvatars = Math.min(children.length, clampedMax - 1);\n  const extraAvatars = Math.max(totalAvatars - clampedMax, totalAvatars - maxAvatars, 0);\n  const extraAvatarsElement = renderSurplus ? renderSurplus(extraAvatars) : `+${extraAvatars}`;\n  let marginValue;\n  if (ownerState.spacing && SPACINGS[ownerState.spacing] !== undefined) {\n    marginValue = SPACINGS[ownerState.spacing];\n  } else if (ownerState.spacing === 0) {\n    marginValue = 0;\n  } else {\n    marginValue = -ownerState.spacing || SPACINGS.medium;\n  }\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      surplus: slotProps.additionalAvatar ?? componentsProps?.additionalAvatar,\n      ...componentsProps,\n      ...slotProps\n    }\n  };\n  const [SurplusSlot, surplusProps] = useSlot('surplus', {\n    elementType: Avatar,\n    externalForwardedProps,\n    className: classes.avatar,\n    ownerState,\n    additionalProps: {\n      variant\n    }\n  });\n  return /*#__PURE__*/_jsxs(AvatarGroupRoot, {\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    style: {\n      '--AvatarGroup-spacing': `${marginValue}px`,\n      // marginValue is always defined\n      ...other.style\n    },\n    children: [extraAvatars ? /*#__PURE__*/_jsx(SurplusSlot, {\n      ...surplusProps,\n      children: extraAvatarsElement\n    }) : null, children.slice(0, maxAvatars).reverse().map(child => {\n      return /*#__PURE__*/React.cloneElement(child, {\n        className: clsx(child.props.className, classes.avatar),\n        variant: child.props.variant || variant\n      });\n    })]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The avatars to stack.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   *\n   * @deprecated use the `slotProps` prop instead. This prop will be removed in v7. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  componentsProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object\n  }),\n  /**\n   * Max avatars to show before +x.\n   * @default 5\n   */\n  max: chainPropTypes(PropTypes.number, props => {\n    if (props.max < 2) {\n      return new Error(['MUI: The prop `max` should be equal to 2 or above.', 'A value below is clamped to 2.'].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * custom renderer of extraAvatars\n   * @param {number} surplus number of extra avatars\n   * @returns {React.ReactNode} custom element to display\n   */\n  renderSurplus: PropTypes.func,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    additionalAvatar: PropTypes.object,\n    surplus: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    surplus: PropTypes.elementType\n  }),\n  /**\n   * Spacing between avatars.\n   * @default 'medium'\n   */\n  spacing: PropTypes.oneOfType([PropTypes.oneOf(['medium', 'small']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The total number of avatars. Used for calculating the number of extra avatars.\n   * @default children.length\n   */\n  total: PropTypes.number,\n  /**\n   * The variant to use.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Person from \"../internal/svg-icons/Person.js\";\nimport { getAvatarUtilityClass } from \"./avatarClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant,\n    colorDefault\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, colorDefault && 'colorDefault'],\n    img: ['img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, classes);\n};\nconst AvatarRoot = styled('div', {\n  name: 'MuiAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], ownerState.colorDefault && styles.colorDefault];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  position: 'relative',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  flexShrink: 0,\n  width: 40,\n  height: 40,\n  fontFamily: theme.typography.fontFamily,\n  fontSize: theme.typography.pxToRem(20),\n  lineHeight: 1,\n  borderRadius: '50%',\n  overflow: 'hidden',\n  userSelect: 'none',\n  variants: [{\n    props: {\n      variant: 'rounded'\n    },\n    style: {\n      borderRadius: (theme.vars || theme).shape.borderRadius\n    }\n  }, {\n    props: {\n      variant: 'square'\n    },\n    style: {\n      borderRadius: 0\n    }\n  }, {\n    props: {\n      colorDefault: true\n    },\n    style: {\n      color: (theme.vars || theme).palette.background.default,\n      ...(theme.vars ? {\n        backgroundColor: theme.vars.palette.Avatar.defaultBg\n      } : {\n        backgroundColor: theme.palette.grey[400],\n        ...theme.applyStyles('dark', {\n          backgroundColor: theme.palette.grey[600]\n        })\n      })\n    }\n  }]\n})));\nconst AvatarImg = styled('img', {\n  name: 'MuiAvatar',\n  slot: 'Img',\n  overridesResolver: (props, styles) => styles.img\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'MuiAvatar',\n  slot: 'Fallback',\n  overridesResolver: (props, styles) => styles.fallback\n})({\n  width: '75%',\n  height: '75%'\n});\nfunction useLoaded({\n  crossOrigin,\n  referrerPolicy,\n  src,\n  srcSet\n}) {\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    image.src = src;\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAvatar'\n  });\n  const {\n    alt,\n    children: childrenProp,\n    className,\n    component = 'div',\n    slots = {},\n    slotProps = {},\n    imgProps,\n    sizes,\n    src,\n    srcSet,\n    variant = 'circular',\n    ...other\n  } = props;\n  let children = null;\n  const ownerState = {\n    ...props,\n    component,\n    variant\n  };\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded({\n    ...imgProps,\n    ...(typeof slotProps.img === 'function' ? slotProps.img(ownerState) : slotProps.img),\n    src,\n    srcSet\n  });\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  ownerState.colorDefault = !hasImgNotFailing;\n  // This issue explains why this is required: https://github.com/mui/material-ui/issues/42184\n  delete ownerState.ownerState;\n  const classes = useUtilityClasses(ownerState);\n  const [ImgSlot, imgSlotProps] = useSlot('img', {\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps: {\n      slots,\n      slotProps: {\n        img: {\n          ...imgProps,\n          ...slotProps.img\n        }\n      }\n    },\n    additionalProps: {\n      alt,\n      src,\n      srcSet,\n      sizes\n    },\n    ownerState\n  });\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(ImgSlot, {\n      ...imgSlotProps\n    });\n    // We only render valid children, non valid children are rendered with a fallback\n    // We consider that invalid children are all falsy values, except 0, which is valid.\n  } else if (!!childrenProp || childrenProp === 0) {\n    children = childrenProp;\n  } else if (hasImg && alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(AvatarFallback, {\n      ownerState: ownerState,\n      className: classes.fallback\n    });\n  }\n  return /*#__PURE__*/_jsx(AvatarRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ...other,\n    ownerState: ownerState,\n    children: children\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * [Attributes](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attributes) applied to the `img` element if the component is used to display an image.\n   * It can be used to listen for the loading error event.\n   * @deprecated Use `slotProps.img` instead. This prop will be removed in v7. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  imgProps: PropTypes.object,\n  /**\n   * The `sizes` attribute for the `img` element.\n   */\n  sizes: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    img: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The shape of the avatar.\n   * @default 'circular'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['circular', 'rounded', 'square']), PropTypes.string])\n} : void 0;\nexport default Avatar;", "'use client';\n\nimport * as React from 'react';\nimport createSvgIcon from \"../../utils/createSvgIcon.js\";\n\n/**\n * @ignore - internal component.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon(/*#__PURE__*/_jsx(\"path\", {\n  d: \"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"\n}), 'Person');", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorDefault', 'circular', 'rounded', 'square', 'img', 'fallback']);\nexport default avatarClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root', 'avatar']);\nexport default avatarGroupClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,SAAuB;AACvB,IAAAC,qBAAsB;AACtB,sBAA2B;;;ACF3B,IAAAC,SAAuB;AACvB,wBAAsB;;;ACDtB,YAAuB;AAMvB,yBAA4B;AAC5B,IAAO,iBAAQ,kBAA2B,mBAAAC,KAAK,QAAQ;AAAA,EACrD,GAAG;AACL,CAAC,GAAG,QAAQ;;;ACTL,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,gBAAgB,YAAY,WAAW,UAAU,OAAO,UAAU,CAAC;AACtI,IAAO,wBAAQ;;;AFMf,IAAAC,sBAA4B;AAC5B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,SAAS,gBAAgB,cAAc;AAAA,IACtD,KAAK,CAAC,KAAK;AAAA,IACX,UAAU,CAAC,UAAU;AAAA,EACvB;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AACA,IAAM,aAAa,eAAO,OAAO;AAAA,EAC/B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,OAAO,GAAG,WAAW,gBAAgB,OAAO,YAAY;AAAA,EACjG;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,YAAY,MAAM,WAAW;AAAA,EAC7B,UAAU,MAAM,WAAW,QAAQ,EAAE;AAAA,EACrC,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU,CAAC;AAAA,IACT,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,eAAe,MAAM,QAAQ,OAAO,MAAM;AAAA,IAC5C;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,SAAS;AAAA,IACX;AAAA,IACA,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,EACF,GAAG;AAAA,IACD,OAAO;AAAA,MACL,cAAc;AAAA,IAChB;AAAA,IACA,OAAO;AAAA,MACL,QAAQ,MAAM,QAAQ,OAAO,QAAQ,WAAW;AAAA,MAChD,GAAI,MAAM,OAAO;AAAA,QACf,iBAAiB,MAAM,KAAK,QAAQ,OAAO;AAAA,MAC7C,IAAI;AAAA,QACF,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,QACvC,GAAG,MAAM,YAAY,QAAQ;AAAA,UAC3B,iBAAiB,MAAM,QAAQ,KAAK,GAAG;AAAA,QACzC,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,CAAC;AACH,EAAE,CAAC;AACH,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA;AAAA,EAEX,WAAW;AAAA;AAAA,EAEX,OAAO;AAAA;AAAA,EAEP,YAAY;AACd,CAAC;AACD,IAAM,iBAAiB,eAAO,gBAAQ;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE;AAAA,EACD,OAAO;AAAA,EACP,QAAQ;AACV,CAAC;AACD,SAAS,UAAU;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,GAAG;AACD,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,KAAK;AAChD,EAAM,iBAAU,MAAM;AACpB,QAAI,CAAC,OAAO,CAAC,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,cAAU,KAAK;AACf,QAAI,SAAS;AACb,UAAM,QAAQ,IAAI,MAAM;AACxB,UAAM,SAAS,MAAM;AACnB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,QAAQ;AAAA,IACpB;AACA,UAAM,UAAU,MAAM;AACpB,UAAI,CAAC,QAAQ;AACX;AAAA,MACF;AACA,gBAAU,OAAO;AAAA,IACnB;AACA,UAAM,cAAc;AACpB,UAAM,iBAAiB;AACvB,UAAM,MAAM;AACZ,QAAI,QAAQ;AACV,YAAM,SAAS;AAAA,IACjB;AACA,WAAO,MAAM;AACX,eAAS;AAAA,IACX;AAAA,EACF,GAAG,CAAC,aAAa,gBAAgB,KAAK,MAAM,CAAC;AAC7C,SAAO;AACT;AACA,IAAM,SAA4B,kBAAW,SAASC,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ,QAAQ,CAAC;AAAA,IACT,YAAY,CAAC;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,WAAW;AACf,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF;AAGA,QAAM,SAAS,UAAU;AAAA,IACvB,GAAG;AAAA,IACH,GAAI,OAAO,UAAU,QAAQ,aAAa,UAAU,IAAI,UAAU,IAAI,UAAU;AAAA,IAChF;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,SAAS,OAAO;AACtB,QAAM,mBAAmB,UAAU,WAAW;AAC9C,aAAW,eAAe,CAAC;AAE3B,SAAO,WAAW;AAClB,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,CAAC,SAAS,YAAY,IAAI,QAAQ,OAAO;AAAA,IAC7C,WAAW,QAAQ;AAAA,IACnB,aAAa;AAAA,IACb,wBAAwB;AAAA,MACtB;AAAA,MACA,WAAW;AAAA,QACT,KAAK;AAAA,UACH,GAAG;AAAA,UACH,GAAG,UAAU;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,kBAAkB;AACpB,mBAAwB,oBAAAC,KAAK,SAAS;AAAA,MACpC,GAAG;AAAA,IACL,CAAC;AAAA,EAGH,WAAW,CAAC,CAAC,gBAAgB,iBAAiB,GAAG;AAC/C,eAAW;AAAA,EACb,WAAW,UAAU,KAAK;AACxB,eAAW,IAAI,CAAC;AAAA,EAClB,OAAO;AACL,mBAAwB,oBAAAA,KAAK,gBAAgB;AAAA,MAC3C;AAAA,MACA,WAAW,QAAQ;AAAA,IACrB,CAAC;AAAA,EACH;AACA,aAAoB,oBAAAA,KAAK,YAAY;AAAA,IACnC,IAAI;AAAA,IACJ,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACF,CAAC;AACH,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAShF,KAAK,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,KAAK,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA,EAC7D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,kBAAAA,QAAU,MAAM;AAAA,IACrB,KAAK,kBAAAA,QAAU;AAAA,EACjB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,KAAK,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,YAAY,WAAW,QAAQ,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAC3I,IAAI;AACJ,IAAO,iBAAQ;;;AGxSR,SAAS,2BAA2B,MAAM;AAC/C,SAAO,qBAAqB,kBAAkB,IAAI;AACpD;AACA,IAAM,qBAAqB,uBAAuB,kBAAkB,CAAC,QAAQ,QAAQ,CAAC;AACtF,IAAO,6BAAQ;;;AJQf,IAAAC,sBAA2C;AAC3C,IAAM,WAAW;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AACV;AACA,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,MAAM;AAAA,IACb,QAAQ,CAAC,QAAQ;AAAA,EACnB;AACA,SAAO,eAAe,OAAO,4BAA4B,OAAO;AAClE;AACA,IAAM,kBAAkB,eAAO,OAAO;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,2BAAmB,MAAM,EAAE,GAAG,OAAO;AAAA,IAC9C,GAAG,OAAO,IAAI;AAAA,EAChB;AACF,CAAC,EAAE,kBAAU,CAAC;AAAA,EACZ;AACF,OAAO;AAAA,EACL,SAAS;AAAA,EACT,eAAe;AAAA,EACf,CAAC,MAAM,sBAAc,IAAI,EAAE,GAAG;AAAA,IAC5B,QAAQ,cAAc,MAAM,QAAQ,OAAO,QAAQ,WAAW,OAAO;AAAA,IACrE,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,gBAAgB;AAAA,MACd,YAAY;AAAA,IACd;AAAA,EACF;AACF,EAAE,CAAC;AACH,IAAM,cAAiC,kBAAW,SAASC,aAAY,SAAS,KAAK;AACnF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACJ,UAAU;AAAA,IACV;AAAA,IACA,YAAY;AAAA,IACZ;AAAA,IACA,MAAM;AAAA,IACN;AAAA,IACA,YAAY,CAAC;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,UAAU;AAAA,IACV;AAAA,IACA,UAAU;AAAA,IACV,GAAG;AAAA,EACL,IAAI;AACJ,MAAI,aAAa,MAAM,IAAI,IAAI;AAC/B,QAAM,aAAa;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,UAAUD,mBAAkB,UAAU;AAC5C,QAAM,WAAiB,gBAAS,QAAQ,YAAY,EAAE,OAAO,WAAS;AACpE,QAAI,MAAuC;AACzC,cAAI,4BAAW,KAAK,GAAG;AACrB,gBAAQ,MAAM,CAAC,wEAAwE,sCAAsC,EAAE,KAAK,IAAI,CAAC;AAAA,MAC3I;AAAA,IACF;AACA,WAA0B,sBAAe,KAAK;AAAA,EAChD,CAAC;AACD,QAAM,eAAe,SAAS,SAAS;AACvC,MAAI,iBAAiB,YAAY;AAC/B,kBAAc;AAAA,EAChB;AACA,eAAa,KAAK,IAAI,eAAe,GAAG,UAAU;AAClD,QAAM,aAAa,KAAK,IAAI,SAAS,QAAQ,aAAa,CAAC;AAC3D,QAAM,eAAe,KAAK,IAAI,eAAe,YAAY,eAAe,YAAY,CAAC;AACrF,QAAM,sBAAsB,gBAAgB,cAAc,YAAY,IAAI,IAAI,YAAY;AAC1F,MAAI;AACJ,MAAI,WAAW,WAAW,SAAS,WAAW,OAAO,MAAM,QAAW;AACpE,kBAAc,SAAS,WAAW,OAAO;AAAA,EAC3C,WAAW,WAAW,YAAY,GAAG;AACnC,kBAAc;AAAA,EAChB,OAAO;AACL,kBAAc,CAAC,WAAW,WAAW,SAAS;AAAA,EAChD;AACA,QAAM,yBAAyB;AAAA,IAC7B;AAAA,IACA,WAAW;AAAA,MACT,SAAS,UAAU,qBAAoB,mDAAiB;AAAA,MACxD,GAAG;AAAA,MACH,GAAG;AAAA,IACL;AAAA,EACF;AACA,QAAM,CAAC,aAAa,YAAY,IAAI,QAAQ,WAAW;AAAA,IACrD,aAAa;AAAA,IACb;AAAA,IACA,WAAW,QAAQ;AAAA,IACnB;AAAA,IACA,iBAAiB;AAAA,MACf;AAAA,IACF;AAAA,EACF,CAAC;AACD,aAAoB,oBAAAE,MAAM,iBAAiB;AAAA,IACzC,IAAI;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA,GAAG;AAAA,IACH,OAAO;AAAA,MACL,yBAAyB,GAAG,WAAW;AAAA;AAAA,MAEvC,GAAG,MAAM;AAAA,IACX;AAAA,IACA,UAAU,CAAC,mBAA4B,oBAAAC,KAAK,aAAa;AAAA,MACvD,GAAG;AAAA,MACH,UAAU;AAAA,IACZ,CAAC,IAAI,MAAM,SAAS,MAAM,GAAG,UAAU,EAAE,QAAQ,EAAE,IAAI,WAAS;AAC9D,aAA0B,oBAAa,OAAO;AAAA,QAC5C,WAAW,aAAK,MAAM,MAAM,WAAW,QAAQ,MAAM;AAAA,QACrD,SAAS,MAAM,MAAM,WAAW;AAAA,MAClC,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,YAAY,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQrF,UAAU,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,iBAAiB,mBAAAA,QAAU,MAAM;AAAA,IAC/B,kBAAkB,mBAAAA,QAAU;AAAA,EAC9B,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,KAAK,eAAe,mBAAAA,QAAU,QAAQ,WAAS;AAC7C,QAAI,MAAM,MAAM,GAAG;AACjB,aAAO,IAAI,MAAM,CAAC,sDAAsD,gCAAgC,EAAE,KAAK,IAAI,CAAC;AAAA,IACtH;AACA,WAAO;AAAA,EACT,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKzB,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,kBAAkB,mBAAAA,QAAU;AAAA,IAC5B,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EACjE,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,SAAS,mBAAAA,QAAU;AAAA,EACrB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrF,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIjB,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,OAAO,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,SAAS,mBAAAA,QAAgD,UAAU,CAAC,mBAAAA,QAAU,MAAM,CAAC,YAAY,WAAW,QAAQ,CAAC,GAAG,mBAAAA,QAAU,MAAM,CAAC;AAC3I,IAAI;AACJ,IAAO,sBAAQ;", "names": ["React", "import_prop_types", "React", "_jsx", "import_jsx_runtime", "Avatar", "_jsx", "PropTypes", "import_jsx_runtime", "useUtilityClasses", "AvatarGroup", "_jsxs", "_jsx", "PropTypes"]}