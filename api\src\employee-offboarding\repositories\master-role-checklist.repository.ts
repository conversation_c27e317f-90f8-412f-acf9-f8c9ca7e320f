import { Injectable } from "@nestjs/common";
import { BaseRepository } from "src/shared/repositories";
import { MasterRoleChecklist } from "../models";

@Injectable()
export class MasterRoleChecklistRepository extends BaseRepository<MasterRoleChecklist> {
    constructor() {
        super(MasterRoleChecklist);
    }

    public async getEntityMasterChecklist(entityId: number): Promise<MasterRoleChecklist[]> {
        return this.findAll({
            where: {
                entityId
            }
        });
    }


}