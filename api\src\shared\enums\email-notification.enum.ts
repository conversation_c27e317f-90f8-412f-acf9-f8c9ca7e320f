export enum EMAIL_TEMPLATES {
	CHECKLIST_TASK = 'EOP.EMAIL.CHECKLIST_TASK',
	DEPARTMENT_CHECKLIST_CLEAR = 'EOP.EMAIL.DEPARTMENT_CHECKLIST_CLEAR',
	DEPENDANT_CHECKLIST_ASSIGNED = 'EOP.EMAIL.DEPENDANT_CHECKLIST_ASSIGNED'
}

export enum USER_EMAIL_GROUP {
	PRODUCT_SUPPORT_EMAIL_GROUP = 'ProductSupportEmailGroup',
}

export enum NOTIFICATION_ENTITY_TYPE {
	CHECKLIST_TASK = 'EMAIL_CHECKLIST_TASK',
}

export enum UI_ROUTES {
	AVAILABLE_CAPABILITY_LIST = '/locations/:locationId/capabilities',
	CAPABILITY_DETAIL = '/capabilities/:capabilityId',
}
